# Environment variables
.env
.env.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Streamlit
.streamlit/secrets.toml

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Data
data/
*.csv
!sample-*.csv
![Sample]*.csv

# Temporary files
tmp/
temp/
*.tmplogs/

# Test outputs
test_output_*.txt
test_output_*.json
scraper_test_results_*.json
prompt_evaluation_*.json
prompt_evaluation_*.csv
prompt_evaluation_*.txt

# Debug outputs
outputs/

# Google service account credentials
sem-ad-copy-demo-*.json
*-service-account.json
client_sheet_structure.json
