# SEM Ad Copy Tool - Issues & Todo Checklist
*Updated: 2025-07-29*

## 🐛 Bugs to Fix

### HIGH PRIORITY
- [x] **Fix brand name defaulting to "Wisp"** ✅ **RESOLVED**
  - **Feedback:** "I used this for a different brand (Farmacy Beauty) but the copy still reflected Wisp."
  - **Production Readiness:** minor_edits
  - **Resolution:** Fixed KeyError in prompt template. Brand field is now properly optional - users can specify a brand in the UI or let the AI infer it from the landing page content. No more hard-coded brand names.
  - **Fixed in commits:** `6b560d8`, `f846a5f`, `3dc1a1a` (July 29, 2025)

## ✨ Features to Implement

### HIGH PRIORITY
- [x] **Add optional brief/context field** ✅ **RESOLVED**
  - **Feedback:** "I used this for Promo messaging but did not have an active landing page so pointed users to the home page. In the event this happens, we may want an optional field to attach some kind of brief so that copy is not generic if using a non promo specific landing page. This could apply for other messaging too, in the even the final LP is not yet ready."
  - **Production Readiness:** ready
  - **Resolution:** Added optional "Brief/Context" text area in UI that allows users to provide additional campaign context when landing pages are generic or non-promotional. The brief is integrated into the AI prompt to enhance copy relevance and specificity.
  - **Fixed in commits:** `cce87c9`, `9796215`, `e8a5e3f` (July 29, 2025)

### MEDIUM PRIORITY
- [ ] **Allow flexible copy type positioning**
  - **Feedback:** "I like the copy type feature for the assets. It might be nice to be able move CTAs to position 2, and other copy types to position 3, for preference reasons."
  - **Production Readiness:** minor_edits

- [ ] **Add review quotes as new copy type**
  - **Feedback:** "Another type of copy for descriptions could be reviews. Locating and finding quotable product / brand reviews on a given LP or brief that fit the character limit would be a great."
  - **Production Readiness:** minor_edits

- [ ] **Add case toggle for descriptions**
  - **Feedback:** "A way to toggle Title case or Sentence case for descriptions."
  - **Production Readiness:** minor_edits

### LOW PRIORITY
- [ ] **Add educational/informative copy types**
  - **Feedback:** "I ran this on a blog page, which is less likely to occur, but realized headline 1 type didn't really cover something like this. Perhaps we could add a Informative type that generates a more education styles of phrases.  

I actually think it did a pretty good job running it as the Brand Mention, but the Headline 2 (Value Props, Immediacy, Urgency) didn't really fit.  There might be room for Copy Type to be something like a Fact, Statistic, or Example."
  - **Production Readiness:** minor_edits

- [ ] **Add collections-specific headline type**
  - **Feedback:** "It might be nice type create a Headline Position 1 Type for Collections. The Product Mention type works well, but in the event we want to use a collections / PLP page it would be nice to distinguish that."
  - **Production Readiness:** minor_edits

## Summary Stats
- Total feedback entries: 7
- Production readiness: 5 minor_edits, 1 ready (now resolved)
- **Resolved:** 1 bug fix ✅ + 1 feature ✅
- **Remaining:** 5 feature requests to implement