# TODO

## CRITICAL/EXISTENTIAL FIXES

### 1. Export EXACT template structure
- Match the exact CSV format from the example template
- Ensure all columns and formatting are identical


### 2. Deploy to Railway
- Set up Railway deployment
- Configure environment variables
- Ensure production readiness
- Fix shared session issue in LandingPageScraper (create new session per request instead of shared instance)

### 3. Gate with password
- Add password protection
- Implement cookie-based session to avoid re-login
- Secure the application access

## NICE-TO-HAVES/IMPROVEMENTS

### 4. API key validation (MVP: not needed since deployed with working key)
- For production: could add pre-generation validation to check ANTHROPIC_API_KEY before scraping
- Would prevent wasted scraping time if key is missing/invalid
- Currently fails fast at generation time which is acceptable for MVP

### 5. Implement Supabase trace/evals
- Port working implementation from another repo
- Set up for tracking generations, performance, and compliance metrics

### 6. Review UI/UX and think of improvements
- Add constants for character limits instead of hardcoding 30/90
- Decide whether to keep/remove "Within Limit" column (always shows ✅ due to Pydantic validation)
- Improve filename sanitization for special characters in CSV export
- Add regenerate button without re-entering inputs
- Optimize variant grouping performance
- Better error messages with specific failure reasons

## DONE

### UI/UX Improvements
- ✅ Fix "Title" label showing URL (should show actual page title) - Scraper correctly extracts page title and displays it in the UI