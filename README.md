# SEM Search Ad Copy Automation

Automated tool for generating Google Ads responsive search ad copy, optimized for character limits and position-specific messaging.

## Features

- AI-powered ad copy generation using Claude 3.5 Sonnet
- Landing page content extraction and analysis
- Character limit validation (30 for headlines, 90 for descriptions)
- Position-specific copy strategies
- Export to CSV in agency-standard format
- Google Sheets integration for direct export
- Optional enhanced logging with Supabase

## Deployment

### Railway Deployment

1. Push code to GitHub repository
2. Connect Railway to your GitHub repo
3. Set the following environment variables in Railway:
   - `ANTHROPIC_API_KEY` - Your Anthropic API key (required)
   - `GOOGLE_SERVICE_ACCOUNT_JSON` - Full JSON content of Google service account (optional, for Sheets export)
   - `SUPABASE_URL` - Your Supabase URL (optional, for logging)
   - `SUPABASE_KEY` - Your Supabase anon key (optional, for logging)
4. Deploy - Railway will automatically detect the Procfile and start the app

### Local Development Setup

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Copy `.env.example` to `.env` and add your API keys:
   ```bash
   cp .env.example .env
   ```

5. Set up Supabase tables (optional, for logging):
   ```sql
   -- Table for generation logs
   CREATE TABLE ad_copy_generations (
     id SERIAL PRIMARY KEY,
     generation_id TEXT UNIQUE NOT NULL,
     request JSONB NOT NULL,
     response JSONB NOT NULL,
     model TEXT NOT NULL,
     duration_ms FLOAT,
     error TEXT,
     created_at TIMESTAMP DEFAULT NOW()
   );

   -- Table for user evaluations
   CREATE TABLE ad_copy_evaluations (
     id SERIAL PRIMARY KEY,
     generation_id TEXT REFERENCES ad_copy_generations(generation_id),
     variant_index INTEGER NOT NULL,
     rating INTEGER CHECK (rating >= 1 AND rating <= 5),
     feedback TEXT,
     created_at TIMESTAMP DEFAULT NOW()
   );
   ```

## Usage

### Running the Application

```bash
streamlit run app.py
```

### How to Use

1. Enter the landing page URL you want to analyze
2. Specify your target keyword
3. Select copy type for position 1 headlines
4. Adjust the number of headlines/descriptions if needed
5. Click "Generate Ad Copy"
6. Export results via CSV download or Google Sheets

### Google Sheets Export

To enable Google Sheets export:
1. Share your target Google Sheet with the service account email
2. Copy the sheet URL when exporting
3. The tool will create a new worksheet tab with timestamp

## Technical Details

- Built with Python and Streamlit
- Uses Anthropic's Claude 3.5 Sonnet for AI generation
- Implements structured output with JSON schemas
- Position-based copy strategies for optimal performance