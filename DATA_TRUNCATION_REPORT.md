# Data Truncation and Limits Report
## SEM Search Ad Copy Automation Codebase

Generated: 2025-07-30

---

## Executive Summary

This report documents all data truncations, character limits, and array slicing operations found in the SEM Search Ad Copy Automation codebase. The analysis reveals several critical truncations that significantly impact the quality of information sent to the AI model, potentially affecting ad copy generation quality.

---

## 1. Critical Data Truncations Affecting AI Input

### 1.1 Landing Page Content Truncations

#### **H1 and H2 Tags - Severe Truncation**
**Location:** `src/services/ad_generator.py` (lines 303-304)
```python
h1_tags = ', '.join(landing_page.h1_tags[:3]) if landing_page.h1_tags else 'N/A'
h2_tags = ', '.join(landing_page.h2_tags[:3]) if landing_page.h2_tags else 'N/A'
```
**Impact:** 
- Only the first 3 H1 and H2 tags are sent to the AI
- Critical product/service information in tags 4+ is completely lost
- AI has incomplete understanding of page structure and content hierarchy

#### **Body Text - NO TRUNCATION in Prompt**
**Location:** `src/services/ad_generator.py` (line 305)
```python
body_preview = landing_page.body_text if landing_page.body_text else 'N/A'
```
**Note:** Despite the variable name `body_preview`, the FULL body text is sent to AI without truncation.

### 1.2 Database Logging Truncations

#### **H1/H2 Tags Database Storage**
**Location:** `src/services/ad_generator.py` (lines 181-182)
```python
landing_page_h1_tags=landing_page.h1_tags[:10] if landing_page.h1_tags else None,
landing_page_h2_tags=landing_page.h2_tags[:10] if landing_page.h2_tags else None,
```
**Impact:** Database stores up to 10 tags (better than AI input which only gets 3)

#### **Body Text Preview for Database**
**Location:** `src/services/ad_generator.py` (line 183)
```python
landing_page_body_preview=landing_page.body_text[:500] if landing_page.body_text else None,
```
**Impact:** Only first 500 characters stored in database for logging purposes

---

## 2. Character Limits for Ad Copy

### 2.1 Headline Character Limits
**Location:** `src/models/ad_schemas.py` (multiple occurrences)
```python
"text": {"type": "string", "maxLength": 30}
```
**Implementation:**
- Hard limit: 30 characters (including spaces)
- Enforced in JSON schema validation
- Post-generation filtering removes headlines > 30 chars

### 2.2 Description Character Limits
**Location:** `src/models/ad_schemas.py` (lines 80, 94)
```python
"text": {"type": "string", "maxLength": 90}
```
**Implementation:**
- Hard limit: 90 characters (including spaces)
- Enforced in JSON schema validation
- Post-generation filtering removes descriptions > 90 chars

### 2.3 Character Validation Logic
**Location:** `src/models/ad_schemas.py` (lines 163-164, 203-204)
```python
# Headlines
valid_items = [item for item in items if len(item.text) <= char_limit]  # char_limit = 30

# Descriptions
valid_items = [item for item in items if len(item.text) <= char_limit]  # char_limit = 90
```
**Impact:** Invalid items are silently filtered out, potentially reducing variant counts

---

## 3. Array Slicing and Collection Limits

### 3.1 Header Tag Extraction Limits
**Location:** `src/services/scraper.py` (lines 173-175)
```python
h1_tags=h1_tags[:10],
h2_tags=h2_tags[:10],
h3_tags=h3_tags[:10],
```
**Impact:** Maximum 10 headers of each type extracted from landing pages

### 3.2 Display Truncations

#### **Generation ID Display**
**Location:** `app.py` (line 441)
```python
f"{generation.generation_id[:8]}..."
```
**Impact:** Only first 8 characters of UUID shown in UI (cosmetic only)

#### **Test Output Preview**
**Location:** `tests/test_scraper.py` (line 65)
```python
print(content.body_text[:1000])
```
**Impact:** Test output shows only first 1000 characters (for readability in tests)

---

## 4. API and Processing Limits

### 4.1 Token Limits
**Location:** `src/services/ad_generator.py` (lines 93, 190, 245, 276)
```python
"max_tokens": 4096
```
**Impact:** Maximum response size from Claude API

### 4.2 Request Generation Buffer
**Location:** `src/services/ad_generator.py` (lines 333-337)
```python
headline_1_count=request.headline_1_count + 2,
headline_2_count=request.headline_2_count + 2,
headline_3_count=request.headline_3_count + 2,
description_1_count=request.description_1_count + 2,
description_2_count=request.description_2_count + 2
```
**Impact:** AI is asked to generate 2 extra variants of each type to account for filtering

---

## 5. Impact Analysis

### High Impact Truncations:
1. **H1/H2 Tag Truncation ([:3])** - Severely limits AI's understanding of page content
2. **Character Limit Filtering** - Silently removes valid but over-limit variants

### Medium Impact Truncations:
1. **Header Tag Storage Limits ([:10])** - May miss important headers on content-rich pages
2. **Database Body Preview ([:500])** - Affects logging/analytics but not AI generation

### Low Impact Truncations:
1. **UUID Display Truncation** - Cosmetic only
2. **Test Output Truncation** - Development/debugging only

---

## 6. Recommendations

### Immediate Actions:
1. **Increase H1/H2 tag limit** from 3 to at least 10 for AI prompt
2. **Add warning messages** when character limit filtering removes variants
3. **Consider dynamic tag selection** based on relevance rather than position

### Future Improvements:
1. **Implement smart truncation** that preserves semantic meaning
2. **Add configuration options** for truncation limits
3. **Create fallback generation** when too many variants are filtered
4. **Add metrics tracking** for truncation frequency and impact

---

## 7. Code References

All truncations found via systematic search for patterns:
- `[:` (array slicing)
- `truncate`, `preview`, `limit`
- `max_length`, `maxLength`
- Numeric limits: `30`, `90`, `500`, `1000`, `4096`

Primary files affected:
- `/src/services/ad_generator.py` - Main truncation logic
- `/src/models/ad_schemas.py` - Character limit validation
- `/src/services/scraper.py` - Header extraction limits
- `/app.py` - Display truncations
- `/prompts/user_prompt_template.txt` - Template receiving truncated data