# Debug Report: Brand Field KeyError Fix

**Date:** July 29, 2025  
**Issue:** Application failing with `'brand'` KeyError during ad copy generation  
**Status:** ✅ RESOLVED

## Problem Description

The application was throwing a `KeyError: 'brand'` during ad copy generation, preventing users from generating ad copy. The error occurred in the ad generation service when trying to format the user prompt template.

### Error Messages
```
2025-07-29 18:31:38,190 - src.services.ad_generator - ERROR - Generation failed: 'brand'
2025-07-29 18:31:52,499 - src.services.ad_generator - ERROR - Generation failed: 'brand'
```

## Root Cause Analysis

The issue was caused by a **mismatch between the prompt template and the code logic** after recent refactoring:

### What Happened
1. **Recent Changes**: The application was refactored to make the `brand` field optional, allowing users to either:
   - Specify a brand explicitly in the UI
   - Let the AI infer the brand from the landing page content

2. **Code Updated**: The `_create_user_prompt()` method in `src/services/ad_generator.py` was updated to handle optional brand logic using:
   - `brand_instruction` variable (contains "Brand: {brand_name}" when specified)
   - `brand_inference_instruction` variable (contains inference instructions when brand not specified)

3. **Template Not Updated**: The prompt template file `prompts/user_prompt_template.txt` still contained the old `{brand}` placeholder

4. **KeyError Result**: When the template formatting tried to substitute `{brand}`, it wasn't found in the formatting parameters, causing a KeyError.

### Code Flow
```python
# In _create_user_prompt() method:
if request.brand:
    brand_instruction = f"Brand: {request.brand}\n"
    brand_inference_instruction = ""
else:
    brand_instruction = ""
    brand_inference_instruction = "Important: Identify the brand name..."

# Template formatting with new variables:
return USER_PROMPT_TEMPLATE.format(
    brand_instruction=brand_instruction,        # ✅ New variable
    brand_inference_instruction=brand_inference_instruction,  # ✅ New variable
    # ... other variables
)
```

But the template still had:
```txt
Brand: {brand}  # ❌ Old placeholder - not in formatting dict
```

## Solution

### 1. Updated Prompt Template
Fixed `prompts/user_prompt_template.txt` to use the correct placeholders:

**Before:**
```txt
Generate Responsive Search Ad (RSA) copy for:

Brand: {brand}
Target Keyword: {target_keyword}
Headline 1 Type: {headline_1_type}
```

**After:**
```txt
Generate Responsive Search Ad (RSA) copy for:

{brand_instruction}Target Keyword: {target_keyword}
Headline 1 Type: {headline_1_type}

{brand_inference_instruction}
```

### 2. How It Works Now
- **With brand specified**: 
  - `brand_instruction` = `"Brand: MUD/WTR\n"`
  - `brand_inference_instruction` = `""`
  
- **Without brand (inference mode)**:
  - `brand_instruction` = `""`
  - `brand_inference_instruction` = `"Important: Identify the brand name from the landing page content..."`

### 3. Additional Cleanup
- Removed unused `rate_limited` import from `src/services/ad_generator.py`
- Fixed type annotation in `src/utils/database_logging_util.py` for better type safety
- Removed redundant `load_dotenv()` call that was causing potential side effects

## Testing

Verified the fix works for both scenarios:

1. **✅ With Brand Specified**: Generates prompt with explicit brand instruction
2. **✅ Without Brand**: Generates prompt with brand inference instruction
3. **✅ No KeyError**: Template formatting completes successfully

## Prevention

To prevent similar issues in the future:

1. **Template-Code Sync**: When refactoring prompt logic, always update both the code AND the template files
2. **Testing**: Include template formatting in unit tests
3. **Documentation**: Document template variables and their expected behavior

## Files Modified

- `prompts/user_prompt_template.txt` - Updated template placeholders
- `src/services/ad_generator.py` - Removed unused import
- `src/utils/database_logging_util.py` - Fixed type annotations

---

**Resolution Time:** ~30 minutes  
**Impact:** High (application was non-functional)  
**Complexity:** Low (template synchronization issue)
