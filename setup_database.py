"""Set up the Supabase database table for ad generation logging."""
import os
from dotenv import load_dotenv
from supabase import create_client
import sys

load_dotenv()

# SQL to create the table
CREATE_TABLE_SQL = """
-- Create the ad_generation_logs table for tracking SEM ad copy generations
CREATE TABLE IF NOT EXISTS ad_generation_logs (
    -- Core fields
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    generation_id TEXT NOT NULL,
    batch_id UUID NOT NULL,
    variant_number INTEGER NOT NULL CHECK (variant_number >= 1 AND variant_number <= 3),
    
    -- Request data
    brand TEXT NOT NULL,
    target_keyword TEXT NOT NULL,
    headline_1_type TEXT NOT NULL,
    headline_1_count INTEGER NOT NULL,
    headline_2_count INTEGER NOT NULL,
    headline_3_count INTEGER NOT NULL,
    description_1_count INTEGER NOT NULL,
    description_2_count INTEGER NOT NULL,
    
    -- Landing page data
    landing_page_url TEXT NOT NULL,
    landing_page_title TEXT,
    landing_page_h1_tags TEXT[],
    landing_page_h2_tags TEXT[],
    landing_page_body_preview TEXT,
    
    -- System data
    system_version TEXT NOT NULL,
    model_name TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'success' CHECK (status IN ('success', 'error', 'timeout')),
    error_message TEXT,
    
    -- Prompt data
    system_prompt TEXT NOT NULL,
    user_prompt TEXT NOT NULL,
    
    -- API parameters
    temperature DECIMAL NOT NULL,
    max_tokens INTEGER NOT NULL,
    top_p DECIMAL NOT NULL,
    
    -- API trace
    api_request JSONB NOT NULL,
    api_response JSONB,
    
    -- Generated content (the actual output)
    generated_headlines_p1 JSONB,  -- Position 1 headlines
    generated_headlines_p2 JSONB,  -- Position 2 headlines  
    generated_headlines_p3 JSONB,  -- Position 3 headlines
    generated_descriptions_p1 JSONB,  -- Position 1 descriptions
    generated_descriptions_p2 JSONB,  -- Position 2 descriptions
    full_variant_data JSONB,  -- Complete variant data
    
    -- Usage metrics
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    latency_ms INTEGER,
    
    -- User tracking
    created_by TEXT,
    
    -- User feedback (from app.py feedback section)
    production_readiness TEXT CHECK (production_readiness IN ('ready', 'minor_edits', 'major_edits', 'unusable')),
    feedback_text TEXT,
    evaluated_at TIMESTAMPTZ
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ad_logs_brand ON ad_generation_logs(brand);
CREATE INDEX IF NOT EXISTS idx_ad_logs_keyword ON ad_generation_logs(target_keyword);
CREATE INDEX IF NOT EXISTS idx_ad_logs_created ON ad_generation_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_logs_batch ON ad_generation_logs(batch_id);
CREATE INDEX IF NOT EXISTS idx_ad_logs_generation ON ad_generation_logs(generation_id);
CREATE INDEX IF NOT EXISTS idx_ad_logs_readiness ON ad_generation_logs(production_readiness) WHERE production_readiness IS NOT NULL;
"""

def setup_database():
    """Create the database table for ad generation logging."""
    # Get credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        print("\nPlease add the following to your .env file:")
        print("SUPABASE_URL=your_project_url")
        print("SUPABASE_KEY=your_anon_key")
        return False
    
    try:
        # Connect to Supabase
        supabase = create_client(supabase_url, supabase_key)
        print("✅ Connected to Supabase")
        
        print("\n🔍 Checking if table exists...")
        
        # Try to query the table to see if it exists
        try:
            result = supabase.table('ad_generation_logs').select("id").limit(1).execute()
            print("✅ Table 'ad_generation_logs' already exists!")
            return True
        except Exception as e:
            if "relation" in str(e) and "does not exist" in str(e):
                print("❌ Table 'ad_generation_logs' does not exist yet")
                
                # Save the SQL to a file
                os.makedirs("sql", exist_ok=True)
                with open("sql/create_ad_generation_logs_table.sql", "w") as f:
                    f.write(CREATE_TABLE_SQL)
                
                # Extract project reference from URL
                project_ref = supabase_url.split('//')[1].split('.')[0]
                
                print(f"\n📋 To create the table:")
                print(f"1. Go to: https://app.supabase.com/project/{project_ref}/sql/new")
                print(f"2. Copy the SQL from: sql/create_ad_generation_logs_table.sql")
                print(f"3. Paste and run the query")
                print(f"\n✅ Then run this script again to verify the setup.")
                
                return False
            else:
                print(f"❌ Error checking table: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Error connecting to Supabase: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Setting up Supabase database for ad generation logging...\n")
    
    if setup_database():
        print("\n✅ Database setup complete! You can now start logging ad generations.")
    else:
        print("\n⚠️  Please complete the setup steps above and run this script again.")
        sys.exit(1)