<PERSON>, how's it going? 

<PERSON>
Hey. Good. How are you? 

<PERSON>
I'm good, I'm good. Thanks for making the time. 

<PERSON>
Yeah. Sorry about yesterday. 

Ben <PERSON>
Shit happens. I'm just. I'm just, you know, I'm just doing my thing. I'm just trying to hustle. I got four kids, I got a mortgage. I'm just trying to. 

<PERSON>
Yeah, for sure. 

<PERSON>
Okay. All right, so. Yeah. So first of all, I appreciate you for putting together that document. I can tell that you put a lot of thought there and I appreciate that. The reason I wanted to. So I don't have anything to show you yet because it made sense for me first to just go over this together and converge on so. So a couple of things. So first, converge on what. What actually makes sense as a first, minimum viable product, if you will. And second, because I actually have already built something for. For. For. For Lunar, for ad copy generation. I also thought it made sense to show that to you and to get your feedback on or rather as a starting point for the discussion around what does the shape of this look like? With the caveat that actually, I mean, in an ideal world. Not an ideal world. I mean the cool thing about these kinds of automations is that once we. It depends. You can't do everything via Slack, but if it's. There's a lot that we could. Like if we prove it out, I can even build something where you could just like literally like a slash command in Slack and you get it right there or something like that, or you get a link and you go there, or it pushes the CSV somewhere. But anyway, so long story short, I want to look at the doc with you and get clear on. Hey, what do we need here that's going to make this useful as a. As a. As a minimum viable product and then show you what I built for that to inform the conversation. Does that make sense? 

<PERSON> Browning
Yeah, for sure. 

<PERSON> <PERSON>
Awesome. How's your day been so far, by the way? 

<PERSON> Browning
Oh, it's a long one. We all are. Yeah. 

Ben Wise
I'm sorry, it's like life is crazy. 

Rob Browning
No, yeah, it's. It's a crazy summer for sure. How about you? 

Ben Wise
Yeah, yeah, also a long day, not gonna lie. 

Rob Browning
But. 

Ben Wise
Yeah. I'm really hoping I can make life easier though. That would be a huge. For me. That's. Yeah. Okay. Any questions or does that make sense? 

Rob Browning
No, that makes sense. Yeah. 

Ben Wise
Where do you want to start? Or should I choose? 

Rob Browning
You can choose, yeah. 

Ben Wise
Okay, let me show you what I built for them and explain the logic because I think that'll prime you to make this more fruitful. Let me open it up real quick. Okay, so this is. So Solen was the one who specified what this will look like or not what this will look like. But sorry, what this will generate as a first proof of concept. And here let me show you. I didn't spend too long on the UI UX because it doesn't make sense. Also, this might not even live as its own app, but the idea was to generate the copy for this kind of ad. And right now it's hard coded. It's Jocko Fuel and the project is the Go energy drink. But at least for that task or for that project, the idea is that you, you have drop down menus and you can choose, hey, which brand you want, which product you want. You can even add information, you can add best performing copy etc to inform this. So right now it's, it's, as I said, it's, it's. There's the brand, there's the product, this is the model Sonic 4, but whatever. And this is just an overview for the, for the team to explain what they need to do. Because there's, you can rate like you can rate each variant or generation and then that goes into a database. And the cool thing about that is that you start to gather data around, you start to get feedback around what's working, what's working, not in the sense of the actual ad performance if they use the copy, but in the sense of some kind of subject matter expert, you know, someone with experience who's written a lot of copy, looking and saying, oh wow, this is pretty damn close. We're saying, hey, this is a bit off. Here's why. Any questions about this before I go on? It's important for me that you understand as well because this will help our project. 

Rob Browning
No, that makes sense. 

Ben Wise
Okay, cool. And this is where you start to understand why it's valuable to develop custom solutions versus working just with a chatbot. So there's a lot of fine grain control here. We can actually. So they can actually decide how many pain points they want. As in like the left side and how many benefits and even how many words per each. Yeah, per each item in either like the pain points or the benefits or whatever it is you call these. And so I'm pretty tired today. Frazzled and tired. Parents. I'll show you this real quick. Let's do. Yeah, let's just keep it like that. It's going to take 20 to 30 seconds. I'll show you what that looks like, and then unless you have any questions, we'll go over to the document. Yeah, man. I've worked with. Oh, here we go. I've worked with so many agency founders and execs and it's not for the faint of heart. Okay, cool. So right now it generates five variants. Obviously we could have decided on more. And as you can see it actually took the custom instructions, which is cool. So parent fuel, that actually works. And then you see the other energy drinks and then the clean energy and then where things get interesting. So you can copy these, but where things get interesting is that they get to. Again, these were solen specification but they get to rate each of these. I thought that then that goes into a database. Then what that means is that when we do this, you don't have to slack me and be like, hey Ben, I thought this is going to be very easy to leave feedback. It might not look like this but the same ideas will apply of, hey, maybe we get it right the first time but if not, no worries. Just with some feedback you can iterate and improve and so then you can save and then it does have an export function. So because I saw that was one of the specifications so you can export it as a CSV eventually I can even push, I can even integrate it with your Google sheets or whatever it is to make life easy again. The reason I wanted to show this to you is because I thought it would. Yeah, seeing something that's actually out there and I think they're supposed to start using it and provide feedback will help your brain go, oh, okay, maybe this is what it should look like. Or maybe these are the things we should focus on. Let me pause there for a moment. Do you have any questions or insights so far? 

Rob Browning
No, this looks good. 

Ben Wise
I know it doesn't quite translate to what we're doing, but. Yeah, sorry, go ahead. 

Rob Browning
No, I was just saying. No, it looks good. I definitely think this solves for some of the more UX issues with like the messaging with paid search. It's going to be a little bit more. 

Ben Wise
It's a different beast. 

Rob Browning
Yeah, yeah, like robotic if you will. 

Ben Wise
Yeah, yeah. Okay, so let's start here again, I appreciate you taking all the time here. So yeah, so let me just open all the things that you sent over and then we'll look at them together. Together. So this is the, this is the deliverable that you want. Only that obviously it's, it's not, it's going to, it's not going to have the like the approved in the live are going to be unchecked. Then let me pause there. Is that correct? You don't necessarily need this interface, you just need the straight up CSV or as a product or as an output. Is that correct? 

Rob Browning
Yeah, we would just need this. The interface would be nice for the feedback portion if we wanted to do another round of it or try something new and then export. I think a CSP would be better because we'll probably use this at scale for a lot of things over and over. So it might get redundant to be copying and pasting it from the interface. This would be the output we would need. It definitely doesn't have to be this pretty. Pretty. 

Ben Wise
Pretty as what? 

Rob Browning
Oh no. Like it doesn't have to be like properly formatted or anything. Usually we use existing like templates for our client. So anything that we can copy and paste over that kind of resembles this, mainly just the asset itself and then the position pinning, everything else wouldn't be needed. 

Ben Wise
So, sorry, when you say everything else, you mean in terms of this interface or are you referring to something else? 

Rob Browning
Oh no, just in that sheet. Like the length of the characters, the existing impressions wouldn't necessarily be needed. 

Ben Wise
I mean, the length of the characters would be very easy to do. That's not an issue. 

Rob Browning
Yeah. 

Ben Wise
And then. Sorry, can you educate me? Pinning. Position pinning. What, What. What does that mean? 

Rob Browning
So this just means the slot that they go in. So like the headline can appear in three different places, the description in two. Usually you want certain things in one, certain things and two certain things in three. So that would really be what that would be for. 

Ben Wise
And how is that, how do you determine the position, the positioning? Is that. I mean, is that so the. 

Rob Browning
In the potential format, this would be something kind of like we would do. But really what I would advocate for is just making sure that each slot has like one kind of thing. So like we wouldn't want like a brand mention in headline one or. And headline two, so. Oh, I see. Yeah. Yeah. So this is like some kind of. This would be like an ideal flow. But really what we're looking for here is not to repeat the same thing twice so that they're not seeing like within 

Ben Wise
the same kind of set within the same quote unquote variant. If we, if we, if we look at, if we look at this. Is that what you're saying? 

Rob Browning
Yep, Correct. 

Ben Wise
So, okay. So, so you have the, the, the first, the second and so, so would this be. I guess I'm trying to understand how the variance would look like. So is this is, is because it says brand mentioned, product mentioned promo, messaging. Is it one either Is it any one of these? 

Rob Browning
Yeah, any of them. It wouldn't be all three. We don't have that much space. 

Ben Wise
And, and would you want like a random combination for each variant or let's say. Or would you, would you want even to like be able to choose? Because I could build a drop down menu kind of like this where you can actually custom, you can actually customize, you can actually go well let me do a product mention, let me do immediacy and then. Or am I thinking about this correctly or. 

Rob Browning
No, no, that would actually be perfect because yeah, we would probably want to select that depending on the campaign design. We would go with one of these formats anyway. So that would be great for the user to be able to select it for each position and then run it. 

Ben Wise
Yeah. What's the difference between urgency and immediacy? 

Rob Browning
I guess it's like whatever I think urgencies by now sale ends tomorrow and immediacy would be delivered to your door in two days. So like, like, yeah, I don't know if there's a real difference between them but like maybe the operational definition of those could be that or something. 

Ben Wise
Yeah. Okay. So yes, I don't. We're not going to do any competitor research initially. We just want to get something good enough out the door and then we can build on it and then eventually it could be really nicely customizable and also it could be something that you can keep call from Slack or whatever to make your life super easy. But for now, for now I think it makes sense to do something like this but not exactly with this kind of interface. So we'll want the dropdown menu so that you can actually decide exactly what the generation like what you're getting and then we'll want to. How should it be? So you'll be able to export it as a CSV. Right. And I can even use this exact template. So this is how it'll look when you open it up. Right. It'll look like something you're used to. 

Rob Browning
Awesome. 

Ben Wise
So it probably makes sense to have. I mean you tell me it probably because this is. I guess what I'm trying to understand is how does this map onto this. 

Rob Browning
Oh, so like headline one is like the position pinning first with like the asset type as headline and then it goes down to two and then three. And then for the descriptions, we just pin those in the slots, too. I have the asset type. Away from that column. But ideally it'd be like first headline, second headline, third headline, first description, second description. So that's kind of how it maps. 

Ben Wise
But if I'm understanding you don't want this, so you don't want it in this format. You actually want them broken out to headline one, headline two, headline three, description one, description two and then repeated headline one, headline two. I really want to make sure I understand correctly and deliver something that's useful. 

Rob Browning
Yeah, yeah, no it would. We can have them all in the same thing because it's responsive. So there the whole set is going to have the option to kind of pick and choose. So anything that's headline pinned in one could appear in the headline. 

Ben Wise
Oh I see. So it doesn't. So, so it, so it doesn't have to be like this. It just has to be like you just need all these different variations. Then you decide how to mix and match. 

Rob Browning
Well, the algorithm will, based on the LP and the keyword, it'll adjust. But we want the set to be related so that we can put it in the same ad group. But yeah, it will mix and match on its own. 

Ben Wise
Let's think about this. Do we need the drop down menus or is it. There's just a world where for instance you get, let's say like so, so, so sorry, just. I think it. So it probably makes sense in the CSV or in the, in the interface to actually have a column that explains which one of which ones of these. That particular copy. Like which copy type that is. 

Rob Browning
No, yeah, that would be great. Actually I never do that, but I will. 

Ben Wise
That's fine. No, but like for a year because then you can see like okay, this is supposed to be a BRAD mention, this is supposed to be a product mention. This is supposed to be promo messaging. This is supposed to be. And then you can decide. Does that make sense? 

Rob Browning
Yeah, that would be great. 

Ben Wise
Okay, cool. And so I don't know if we need the drop down menus because the cool thing about AI is I could just have it generate like 555555 and you can, I don't know, I'm trying to get from you what would make this the most useful. Because we want to create something that you can just take and use and it fits into not just your workflow but your mental model for how you do this regularly. So what does that look like? Is that for instance, is that. So it's against a landing page. Always correct. 

Rob Browning
Always. 

Ben Wise
Okay. And that's very easy by the way. It's very easy for me to incorporate something that just that just where you can. So ideally, as one of the inputs is the landing page, because I can fetch the text and have it inform the generation. So you're putting in the landing page, Right? You're putting in. We'll have WISP hard coded and the landing page also corresponds to the product. Right, so. So that's both or. 

Rob Browning
No, it will. Yeah. 

Ben Wise
Or there's separate specs. Like do we need, Is the landing page enough for the product specs as well? Or do we need the product specs in addition? Like do you have, do you have some kind of. Yeah, I'm trying to. 

Rob Browning
Usually our landing pages are going to be either PDPs or PLPs, so most of the information's on it. I don't think we need too many product details. I think the one thing details we would need at some point is if we were doing like a promo, we would need to give the promo details because that's never going to be on an lp. But for the most part for like, 

Ben Wise
so maybe for the first version that's enough. We just start there and see it's also. It's not that many. It's not that many words, right? 

Rob Browning
No, it's very limited. 

Ben Wise
I mean the way I'm envisioning it is you get a set of options and eventually like we could even, we can even have it like a button in the spreadsheet or some other way where you're just like, oh, I like this one, I like this one, I like this one. And then that's what gets pushed maybe to another one. I mean, I don't know if we'll do that immediately. I'm just kind of trying to. 

Rob Browning
Yeah. Check in with you. 

Ben Wise
What would make it the most useful? 

Rob Browning
Yeah, that would be good because either if we review it or the client reviews it, we can like supply the feedback directly and then regenerate. That would be great because there's always edits, always things like that. 

Ben Wise
Yeah. And so the idea is. So if I'm just very quick. Sorry, we're going over. Is that okay? 

Rob Browning
Oh yeah, that's fine. 

Ben Wise
Okay, cool. This is my last call. I just need to make sure that I have what I need so that I don't go off and build something useless. So it's, it's kind of like. Yeah, I could see how this would be kind of soul sucking in the sense that you're just coming up with different ways of saying the same things thing and just kind of going, oh yeah, I guess that's better. And Right. So, so, so I understand why this would be a good, you know, use case for AI because you could just look at it and you're like, oh yeah, these three are great. Oh, I'm just going to change slightly rephrase this. And then also talk to me about the number of words for a moment. Are there best practices in terms of. Because as you can see, this is why I wanted you to see, we can actually decide on the number of words for each thing. And again, that's the nice thing about working, not just through a chatbot. Are there constraints or best practices around the number of words for each? 

Rob Browning
Not like the number of words, but there are character limits. 

Ben Wise
We could do that as well. It might not be as accurate, but it can be pretty close. 

Rob Browning
Oh, cool. Yeah. So, like, headlines are thorough. 30 and then descriptions are 90. I don't know why I wrote four, but yeah. 

Ben Wise
Oh, sorry, you have that here. Okay. 

Rob Browning
Yeah, well, it was wrong, so sorry. 

Ben Wise
No, I do. I, I, I did not sleep well last night and it's been. Anyway, whatever. Okay, so headline description. Okay, so you have that here. Sorry, my bad. So this, so this is the max. Okay. 

Rob Browning
Yep. Max. 

Ben Wise
Sorry, I was just thinking in my head, how do I get them up? Just for your information, behind the scenes, what's going to happen is that the model is going to generate it and then the model is going to run a script by code that checks the character length. And if it's over the character length, it's going to trim it and then it's going to run, use the tool again until it gets to under 30 or under 90. Sorry, I just. 

Rob Browning
No, that makes sense. Yeah, just like when I've used AI, that's the process. Usually it's like, oh, okay, those are great headlines. But now, like make it the character length. 

Ben Wise
Well, the cool thing. No, no. So it's going to do all that automatically. Yeah, you're not going to have to worry about that. So, so these are hard, right? Like these, this has to be, it can't be more than 30 characters. And now did these include spaces or not? 

Rob Browning
You know. Yeah. 

Ben Wise
Okay, so these, so both of them includes space. Yeah, that's usually the, the case obviously, because otherwise you'd have some like, weird. 

Rob Browning
Yeah. And then the, the only other thing I'll say with like regards to like the editorial stuff is like for descriptions, typically it like Google will truncate them for us, like just because it feels like it. So what I usually do is I keep everything within one sentence. So if there are times where they want to create like two sentences in the same description, I try to stay away from that. 

Ben Wise
Okay, so, so okay, so that's another constraint here. Good, good. I'm happy we're having this conversation. Now, do we have the, do you have a brand voice guidelines for wisp? 

Rob Browning
I can fetch one, yeah. 

Ben Wise
Oh, so you do have that. You do have like, you have an actual document that you can. 

Rob Browning
Yeah, yeah, I can send that over. It's pretty lengthy, but I can try to just find the copy portion. 

Ben Wise
No, I'll make your life easy. I'm going to skim over this and think about whether I need anything. Why don't you go ahead and do it right now? Because I think this works better when I don't give you too much homework. No. 

Rob Browning
Okay. Yeah, that sounds good. 

Ben Wise
Yeah. Do you want to look for it right now and just slack it to me or. 

Rob Browning
Yeah. Yeah. Okay, cool. 

Ben Wise
Okay. 

Rob Browning
Try to. 

Ben Wise
I'll look at this. 

Rob Browning
Okay. So. 

Ben Wise
Okay, this will wait. This. Eventually we can incorporate this later. The, like, showing it examples of what's performed. Well, for the initial version. I'm not. I'm not going to do that if that's. Okay. 

Rob Browning
Okay. Oh, well, the doc they gave us is actually just a list of paid social copy. It's not actually a brand guidelines. But I'll. 

Ben Wise
I'll. I'll figure it out. 

Rob Browning
Yeah, it's fine that you say that the really like way too much on it because we are limited with copy and a lot of the landing page language will automatically be for their brand voice. 

Ben Wise
That's what I was going to say. I was going to say that I think it can. I think it'll probably. It can get it from the actual landing page. All right, that's fine. Don't worry about that. I just want to make sure I understand. The AI is to come up with the keywords based on what's on the landing page. Correct. It's not that you're. 

Rob Browning
There's two different ways. We supply at target keywords, which is a majority of the time. There is a dynamic search ad campaign now that will target whatever based on the landing page. So the landing page is probably more important than the target keywords at this point. But the target keywords definitely way. 

Ben Wise
So hold on a second. So you. So you don't need the. I'm starting to. So you don't need it to generate the keywords. You just need it to generate for now, like all of this given 

Rob Browning
we would want to give it. The keywords, just because the ad copy does better performance wise from like a quality score standpoint if it features a keyword that we're actually targeting in the ad group. 

Ben Wise
So yeah, so you need. So for now you're feeding it the keyword, like here, you're inputting the keyword. It's not generating the keyword. 

Rob Browning
No, no, no, no. 

Ben Wise
Okay, so you have. So is this the keyword here or no. Or this is the actual copy. 

Rob Browning
This is kind of our optimized copy. The campaign is more about like delay your period. So like the phrase delay your period is what we're targeting. Among other things. But that would be like the central keyword that we would want to optimize for. 

Ben Wise
And does that have to be in the headline or. Not necessarily. 

Rob Browning
Yeah, there could be examples where it doesn't need to verbatim. It doesn't really, but the idea has to be present. So if I'm not saying delay your period. 

Ben Wise
Oh, okay. That's what I was trying to ask. So it doesn't have to say that verbatim. But if. Okay, so. So you're. The AI isn't coming up with the keywords for now, you're inputting the keyword and the landing page and then the output is different sets of this, correct? 

Rob Browning
Yeah. 

Ben Wise
Okay. I just want to make sure I understand 100%. And we say two, three sets of responsive search ads. You mean two, three sets of two or three sets of this? 

Rob Browning
Well, it would be more like if you go back to the sheet, like that first block would be a set in my. In my framework. 

Ben Wise
Okay. Oh, so this is headline one, this is headline two, and then this is description one. Description two. Where's the cta? 

Rob Browning
Oh, it is the second description. Oh, well, that one. That format might not follow these exactly, but description 2 will have like a CTA at the end. Like that pickup today is a. Like, that's. That's all it needs. Or like a shop now. Like, it doesn't have to be fancy. 

Ben Wise
Okay, so do you want it to generate the CTA or. No need. Oh, I mean, it's easy. It's. 

Rob Browning
That would be nice to do. 

Ben Wise
Yeah. Okay. Yeah, yeah, sure, I'll take it. Okay, so I think we're pretty close. If. If. So I think what we need to figure out is. We talked about this a bit before, but what this looks like in terms of. Like, do you just want to like five headline one brand mentions, five product mentions, five pro. Like just five of each or like what's, what's good for you in terms of. You put in, you put in a keyword, you put in a landing page. What do you, what do you want to get back? 

Rob Browning
I would say for like all the others, like Headline two, Headline three, Description one, Description two, we could have multiple of whatever there. So five of whatever. But for that headline one, we probably only want one set of brand mentions, one set of product mentions, or one set of promo mentions. And that would just be like inherent to whatever we were like creating. So if I like go into it, 

Ben Wise
right, you're saying you can't fully mix and match. There has to like this informs the rest. So for this. 

Rob Browning
Yeah. 

Ben Wise
So for one of these. So is it one of these? And then a number of. Of of. So, so if there's a brand mention, is there. Are there like three urgency two, Sorry, it's sorry, Headline two urgency options or. 

Rob Browning
No, I would just say if we go with brand mention, then like does Headline two could have like multiple USPS urgency and immediacy as option. But if we go with the brand mention in headline one, we wouldn't necessarily need any options for product mentions and promo messaging in that set. So like headline one kind of is the only one that needs to be specified, I would say. And then the rest of them can just be whatever. 

Ben Wise
So you mean each set would only have one. Like, no matter how many sets we do, there would be like one product mention. And then you're okay if they're like, hey, here are a couple options for this, couple options for this, couple options for this, et cetera. 

Rob Browning
Yeah. 

Ben Wise
Is that correct? Yeah, correct. So for each headline one, how many options of. Let's say headline two, USPs, UVPs, headline two urgency, headline two, immediacy, how many options do you want for each? 

Rob Browning
I would say we have 15 headline like slots. So the first one, if we could definitely do at least six or seven. And then headline two could be, you know, five and then the rest would be headline three. Just because headline three doesn't always show. So we just need numerically less of those than anything else. 

Ben Wise
So sorry, for one of these, you're saying seven Headline two unique selling propositions seven, Headline two urgency seventh or no. 

Rob Browning
Oh no, not each. Just within that. And it doesn't have to be a number of each. Like just that many options for headlines. You know what? 

Ben Wise
I'll make life easy. I'll just I'll just like here. You could just choose, right? So that we don't have to finalize that. Now the idea is if we look at here, you will get. Let's say you will get. Hold on, let me just. So headline one, it'll specify which one it is. Then maybe that's like this. And then you'll get. For instance, it won't be displayed like this. It'll be a table to make your life easier. But then, but then you can choose like how many of these do you want for that? How many of these do you want for that? How many of these? How many of these? How many of these? How many of these? And then you can. And then it'll keep your settings so you can generate. Export the CSV generate. Does that sound good? 

Rob Browning
Yeah, that's perfect actually. Thank you. 

Ben Wise
Okay. Think that's all I need. Is there anything we left out or does it sound like we're aligned? 

Rob Browning
No, I think that's a good starting point. I know it's like hard to wrap. The non search people never understand response. 

Ben Wise
Yeah, I'm sorry. I have, like, this was, this was very easy because I've done bad copywriting and now I'm like, I'm still learning. So thanks for your patience and for educating me. 

Rob Browning
Hopefully I wasn't too, like, confusing. It's the end of the day. So I'm like, fried. But no, you're fine. 

Ben Wise
Me too. But you're doing great. Okay. Awesome. Okay. And look, when I deliver this, I, I want you to be. Yeah. Just very honest. Like the purpose, like this is meant to save you. And I've, I've done this for my agency, I've done this for other clients. So like, it, it takes. Unfortunately there's like this bit of dance in the beginning and feedback, but then it works. And not only does it work, you can even decide how you want it. Like literally from Slack, you could be like, hey, generate, whatever. And so it's, it's, it's kind of. It's a bit like magic. So thank you for your patience and I'm going to make sure it's worth it. 

Rob Browning
I'm excited. Yeah, I think it'll be easier once we get it up and going because Search Copy is much more formulaic and robotic. 

Ben Wise
Yes. No, this is, Look, I still want these. I still want the guidelines for the. Not the guidelines for the, like the list of rules or whatever it is for the audit. I can do that. That's more work, but I can do that. Actually, since we talked, I've had some interesting ideas and I've done some research. And I think there's a where some of it, as I said, some of it is more deterministic, but there's also an agent, but there's a way for you to verify its work. Like, we can do that and that, and that's cool. But you're right that this is, this is a much lower hanging fruit in the sense that, you know, if I can, I can build this in a day and get, I mean, I'm not saying I'll do it tomorrow, but like, I could do this in a day and just get it to you and, and get the ball rolling. All right. Cool. Awesome. 

Rob Browning
Yeah. 

Ben Wise
Thank you so much. I appreciate you. 

Rob Browning
Thank you. Let me know if you need anything else, but just let me know whenever you have something for me to test. And I'll still give you the editorial guideline, but you can. Guidelines for the audit. Yeah. 

Ben Wise
All right. Thanks so much. Appreciate you. Get some rest. Talk soon. 

Rob Browning
Bye. 

Ben Wise
Bye.