"""Web scraping service for landing page content extraction."""
import requests
from bs4 import BeautifulSoup, Tag
import trafilatura
from typing import Optional, List
import logging
from urllib.parse import urlparse
from ..models.ad_copy import LandingPageContent

logger = logging.getLogger(__name__)

# Suppress non-actionable trafilatura warnings
logging.getLogger('trafilatura').setLevel(logging.ERROR)


class LandingPageScraper:
    """Scrapes and extracts full content from landing pages using BeautifulSoup with trafilatura fallback."""
    
    def __init__(self, timeout: int = 15):
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def _extract_headers(self, soup: BeautifulSoup, tag: str, limit: int = 10) -> List[str]:
        """Extract text content from specified header tags.
        
        Args:
            soup: BeautifulSoup object to search in
            tag: HTML tag name (h1, h2, h3, etc.)
            limit: Maximum number of headers to return
            
        Returns:
            List of header text content, limited to specified count
        """
        return [h.get_text(strip=True) for h in soup.find_all(tag) if h.get_text(strip=True)][:limit]
    
    def extract_content(self, url: str) -> Optional[LandingPageContent]:
        """Extract comprehensive content from landing page."""
        try:
            # Primary method: Use BeautifulSoup (faster and more complete)
            content = self._extract_with_beautifulsoup(url)
            if content:
                return content
            
            # Fallback: Use trafilatura (better at handling complex pages)
            logger.info(f"BeautifulSoup extraction failed for {url}, trying trafilatura")
            return self._extract_with_trafilatura(url)
            
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            return None
    
    def _extract_with_trafilatura(self, url: str) -> Optional[LandingPageContent]:
        """Fallback extraction method using trafilatura for intelligent extraction."""
        logger.debug(f"Extracting content from {url} using Trafilatura")
        try:
            # Fetch URL with trafilatura
            downloaded = trafilatura.fetch_url(url)
            if not downloaded:
                return None
            
            # Extract main content
            text_content = trafilatura.extract(
                downloaded,
                include_links=False,
                include_images=False,
                include_tables=True,
                deduplicate=True,
                target_language='en',
                favor_precision=True
            )
            
            # Also get metadata
            metadata = trafilatura.extract_metadata(downloaded)
            
            if not text_content:
                return None
            
            # Parse HTML for structured data
            soup = BeautifulSoup(downloaded, 'lxml')
            
            # Extract headers
            h1_tags = self._extract_headers(soup, 'h1')
            h2_tags = self._extract_headers(soup, 'h2')
            h3_tags = self._extract_headers(soup, 'h3')
            
            # Get title and meta description
            title = soup.find('title')
            title_text = title.get_text(strip=True) if title else (metadata.title if metadata else '')
            
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            meta_desc_text = ''
            if meta_desc and isinstance(meta_desc, Tag):
                content = meta_desc.get('content', '')
                meta_desc_text = str(content).strip() if content is not None else ''
            elif metadata:
                meta_desc_text = metadata.description or ''
            
            # Use trafilatura's extracted text as body text (full content, not truncated)
            body_text = text_content
            
            return LandingPageContent(
                url=url,
                title=title_text,
                meta_description=meta_desc_text,
                h1_tags=h1_tags,  # Already limited by _extract_headers
                h2_tags=h2_tags,
                h3_tags=h3_tags,
                body_text=body_text  # Full content, not truncated
            )
            
        except Exception as e:
            logger.error(f"Trafilatura extraction failed for {url}: {e}")
            return None
    
    def _extract_with_beautifulsoup(self, url: str) -> Optional[LandingPageContent]:
        """Primary extraction method using BeautifulSoup."""
        logger.debug(f"Extracting content from {url} using BeautifulSoup")
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'lxml')
            
            # Remove script and style elements
            for script in soup(["script", "style", "noscript"]):
                script.decompose()
            
            # Extract meta tags
            title = soup.find('title')
            title_text = title.get_text(strip=True) if title else None
            
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            meta_desc_text = None
            if meta_desc and isinstance(meta_desc, Tag):
                content = meta_desc.get('content', '')
                meta_desc_text = str(content).strip() if content else None
            
            # Extract headers
            h1_tags = self._extract_headers(soup, 'h1')
            h2_tags = self._extract_headers(soup, 'h2')
            h3_tags = self._extract_headers(soup, 'h3')
            
            # Try to find main content area
            main_content = None
            for selector in ['main', 'article', '[role="main"]', '.main-content', '#main-content', '.content', '#content']:
                element = soup.select_one(selector)
                if element:
                    main_content = element
                    break
            
            # If no main content found, use body
            if not main_content:
                main_content = soup.body if soup.body else soup
            
            # Extract all text from main content
            body_text = main_content.get_text(separator=' ', strip=True)
            
            # Clean up the text
            lines = [line.strip() for line in body_text.split('\n') if line.strip()]
            body_text = ' '.join(lines)
            
            # Remove excessive whitespace
            import re
            body_text = re.sub(r'\s+', ' ', body_text)
            
            return LandingPageContent(
                url=url,
                title=title_text,
                meta_description=meta_desc_text,
                h1_tags=h1_tags[:10],
                h2_tags=h2_tags[:10],
                h3_tags=h3_tags[:10],
                body_text=body_text  # Full content, not truncated
            )
            
        except requests.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error parsing {url}: {e}")
            return None
    
    def validate_url(self, url: str) -> bool:
        """Validate URL before scraping."""
        try:
            result = urlparse(url)
            return all([result.scheme in ['http', 'https'], result.netloc])
        except:
            return False