"""Ad copy generation service with structured JSON output."""
import os
import time
import uuid
from typing import Optional
from pathlib import Path
from anthropic import <PERSON>throp<PERSON>, APIError, APIConnectionError, RateLimitError
import logging

from ..models.ad_copy import AdCopyRequest, AdCopyGeneration, AdCopyVariant, LandingPageContent
from ..models.ad_schemas import create_ad_copy_json_schema, StructuredAdCopyGeneration
from ..utils.api_helpers import retry_with_exponential_backoff, RateLimiter

logger = logging.getLogger(__name__)

# Load prompts
PROMPTS_DIR = Path(__file__).parent.parent.parent / "prompts"
SYSTEM_PROMPT = (PROMPTS_DIR / "system_prompt.txt").read_text()
USER_PROMPT_TEMPLATE = (PROMPTS_DIR / "user_prompt_template.txt").read_text()


class AdCopyGenerator:
    """Handles ad copy generation using structured JSON output."""
    
    def __init__(self) -> None:
        self.api_key = None
        self.client = None
        self.model = "claude-sonnet-4-20250514"
        
        # Initialize rate limiter (10 calls per minute, adjust as needed)
        self.rate_limiter = RateLimiter(calls_per_minute=10)
        
        # Initialize enhanced logging
        try:
            from ..utils.database_logging_util import log_generation
            from ..models.generation_log import GenerationLogEntry
            self.log_generation = log_generation
            self.GenerationLogEntry = GenerationLogEntry
            self.logging_enabled = True
            self.system_version = "1.0.1"
        except Exception as e:
            logger.warning(f"Enhanced logging not available: {e}")
            self.logging_enabled = False
        
    def generate(self, request: AdCopyRequest, landing_page: LandingPageContent) -> Optional[AdCopyGeneration]:
        """Generate ad copy variations using structured output."""
        generation_id = str(uuid.uuid4())
        batch_id = uuid.uuid4() if self.logging_enabled else None
        
        # Initialize client on first use if not already done
        if not self.client:
            self.api_key = os.getenv("ANTHROPIC_API_KEY")
            if not self.api_key:
                logger.error("ANTHROPIC_API_KEY not configured. Please add it to your .env file.")
                return None
            
            try:
                self.client = Anthropic(api_key=self.api_key)
                logger.info("Anthropic client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic client: {e}")
                return None
        
        try:
            # Create JSON schema based on requested counts
            json_schema = create_ad_copy_json_schema(
                headline_1_count=request.headline_1_count,
                headline_2_count=request.headline_2_count,
                headline_3_count=request.headline_3_count,
                description_1_count=request.description_1_count,
                description_2_count=request.description_2_count
            )
            
            # Create system prompt
            system_prompt = self._create_system_prompt()
            
            # Create user prompt
            user_prompt = self._create_user_prompt(request, landing_page)
            
            # Track timing
            start_time = time.time()
            
            # Create message with tool calling for structured output
            response = self._make_api_call(system_prompt, user_prompt, json_schema)
            duration_ms = (time.time() - start_time) * 1000
            logger.info(f"API call completed in {duration_ms:.0f}ms")
            
            # Capture API trace for enhanced logging
            api_request = {
                "model": self.model,
                "messages": [{"role": "user", "content": user_prompt}],
                "temperature": 0.7,
                "max_tokens": 4096,
                "top_p": 1.0,
                "system": system_prompt,
                "tools": [{"name": "output_ad_copy", "description": "Output the generated ad copy in structured format", "input_schema": json_schema}],
                "tool_choice": {"type": "tool", "name": "output_ad_copy"}
            }
            api_response = {
                "id": response.id,
                "model": response.model,
                "stop_reason": response.stop_reason,
                "usage": response.usage.model_dump() if hasattr(response.usage, 'model_dump') else dict(response.usage)
            }
            
            # Validate stop reason
            if response.stop_reason != "tool_use":
                logger.error(f"Unexpected stop reason: {response.stop_reason}. Expected 'tool_use'")
            
            # Parse response from tool use
            generated_json = None
            for content in response.content:
                if hasattr(content, 'type') and content.type == "tool_use":
                    if hasattr(content, 'name') and content.name == "output_ad_copy":
                        if hasattr(content, 'input'):
                            generated_json = content.input
                        break
            
            if not generated_json:
                logger.error(f"No tool use found in response. Stop reason: {response.stop_reason}")
                raise ValueError("No tool use found in response")
            
            
            # Ensure generated_json is a dictionary (tool input should already be parsed)
            if not isinstance(generated_json, dict):
                raise ValueError(f"Expected dict from JSON response, got {type(generated_json)}")
                
            # Parse the structured output
            structured_output = StructuredAdCopyGeneration(**generated_json)
            
            # Pass requested counts for filtering
            requested_counts = {
                'headline_1_count': request.headline_1_count,
                'headline_2_count': request.headline_2_count,
                'headline_3_count': request.headline_3_count,
                'description_1_count': request.description_1_count,
                'description_2_count': request.description_2_count,
            }
            
            # Convert to variants with filtering
            variant_dicts = structured_output.to_variants(requested_counts)
            variants = []

            # Create variants (all should be valid after filtering)
            for v in variant_dicts:
                variant = AdCopyVariant(**v)
                variants.append(variant)
            
            # Create generation object
            generation = AdCopyGeneration(
                request=request,
                landing_page_content=landing_page,
                variants=variants,
                generation_id=generation_id
            )
            
            # Enhanced logging - log once for the entire generation
            if self.logging_enabled and batch_id:
                try:
                    # Group variants by position
                    headlines_p1 = [v.model_dump() for v in variants if v.asset_type == "Headline" and v.position_pinning == "1st"]
                    headlines_p2 = [v.model_dump() for v in variants if v.asset_type == "Headline" and v.position_pinning == "2nd"]
                    headlines_p3 = [v.model_dump() for v in variants if v.asset_type == "Headline" and v.position_pinning == "3rd"]
                    descriptions_p1 = [v.model_dump() for v in variants if v.asset_type == "Description" and v.position_pinning == "1st"]
                    descriptions_p2 = [v.model_dump() for v in variants if v.asset_type == "Description" and v.position_pinning == "2nd"]
                    
                    log_entry = self.GenerationLogEntry(
                        generation_id=generation_id,
                        batch_id=batch_id,
                        variant_number=1,  # Single entry for entire generation
                        brand=request.brand or "Inferred from landing page",
                        target_keyword=request.target_keyword,
                        headline_1_type=request.headline_1_type,
                        headline_1_count=request.headline_1_count,
                        headline_2_count=request.headline_2_count,
                        headline_3_count=request.headline_3_count,
                        description_1_count=request.description_1_count,
                        description_2_count=request.description_2_count,
                        landing_page_url=landing_page.url,
                        landing_page_title=landing_page.title,
                        landing_page_h1_tags=landing_page.h1_tags[:10] if landing_page.h1_tags else None,
                        landing_page_h2_tags=landing_page.h2_tags[:10] if landing_page.h2_tags else None,
                        landing_page_body_preview=landing_page.body_text[:500] if landing_page.body_text else None,
                        system_version=self.system_version,
                        model_name=self.model,
                        status="success",
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        temperature=0.7,
                        max_tokens=4096,
                        top_p=1.0,
                        api_request=api_request,
                        api_response=api_response,
                        generated_headlines_p1=headlines_p1,
                        generated_headlines_p2=headlines_p2,
                        generated_headlines_p3=headlines_p3,
                        generated_descriptions_p1=descriptions_p1,
                        generated_descriptions_p2=descriptions_p2,
                        full_variant_data=generated_json,
                        prompt_tokens=response.usage.input_tokens if hasattr(response.usage, 'input_tokens') else None,
                        completion_tokens=response.usage.output_tokens if hasattr(response.usage, 'output_tokens') else None,
                        total_tokens=(response.usage.input_tokens + response.usage.output_tokens) if hasattr(response.usage, 'input_tokens') else None,
                        latency_ms=int(duration_ms)
                    )
                    result = self.log_generation(log_entry)
                    if result:
                        logger.info(f"✓ Logged generation to database")
                    else:
                        logger.warning("Failed to log generation to database")
                except Exception as e:
                    logger.warning(f"Failed to log generation: {e}")
            
            return generation
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            
            # Log error with enhanced logging
            if self.logging_enabled and batch_id:
                try:
                    # Use locals() to check if variables were defined before the error
                    error_system_prompt = locals().get('system_prompt', '')
                    error_user_prompt = locals().get('user_prompt', '')
                    
                    log_entry = self.GenerationLogEntry(
                        generation_id=generation_id,
                        batch_id=batch_id,
                        variant_number=1,
                        brand=request.brand or "Inferred from landing page",
                        target_keyword=request.target_keyword,
                        headline_1_type=request.headline_1_type,
                        headline_1_count=request.headline_1_count,
                        headline_2_count=request.headline_2_count,
                        headline_3_count=request.headline_3_count,
                        description_1_count=request.description_1_count,
                        description_2_count=request.description_2_count,
                        landing_page_url=landing_page.url,
                        system_version=self.system_version,
                        model_name=self.model,
                        status="error",
                        error_message=str(e),
                        system_prompt=error_system_prompt,
                        user_prompt=error_user_prompt,
                        temperature=0.7,
                        max_tokens=4096,
                        top_p=1.0,
                        api_request={},
                        latency_ms=0
                    )
                    result = self.log_generation(log_entry)
                    if not result:
                        logger.warning("Failed to log error generation to database")
                except Exception as log_error:
                    logger.warning(f"Failed to log error: {log_error}")
            
            return None
    
    def _make_api_call(self, system_prompt: str, user_prompt: str, json_schema: dict):
        """Make API call with retry and rate limiting."""
        
        # Apply rate limiting manually
        self.rate_limiter.wait_if_needed()
        
        # Define the API call function with retry decorator
        @retry_with_exponential_backoff(
            max_retries=3,
            initial_delay=2.0,
            max_delay=60.0,
            exceptions=(APIError, APIConnectionError, RateLimitError)
        )
        def _api_call():
            if not self.client:
                raise ValueError("Anthropic client not initialized")
            return self.client.messages.create(
                model=self.model,
                max_tokens=4096,
                temperature=0.7,
                system=system_prompt,
                messages=[{
                    "role": "user",
                    "content": user_prompt
                }],
                tools=[
                    {
                        "name": "output_ad_copy",
                        "description": "Output the generated ad copy in structured format",
                        "input_schema": json_schema
                    }
                ],
                tool_choice={"type": "tool", "name": "output_ad_copy"}
            )
        
        # Execute the API call with retry logic
        return _api_call()
    
    def _create_system_prompt(self) -> str:
        """Return the system prompt for ad generation."""
        return SYSTEM_PROMPT
    
    def _create_user_prompt(self, request: AdCopyRequest, landing_page: LandingPageContent) -> str:
        """Create user prompt with context."""
        # Format landing page elements
        h1_tags = ', '.join(landing_page.h1_tags[:3]) if landing_page.h1_tags else 'N/A'
        h2_tags = ', '.join(landing_page.h2_tags[:3]) if landing_page.h2_tags else 'N/A'
        body_preview = f"{landing_page.body_text[:500]}..." if landing_page.body_text else 'N/A'
        
        # Handle optional brand
        if request.brand:
            brand_instruction = f"Brand: {request.brand}\n"
            brand_inference_instruction = ""
        else:
            brand_instruction = ""
            brand_inference_instruction = "Important: Identify the brand name from the landing page content (title, H1 tags, or body text) and use it appropriately in the ad copy, especially for brand_mention type headlines."

        # Handle optional brief/context
        if request.brief:
            brief_context = f"Additional Campaign Context:\n{request.brief}\n\nUse this context to enhance the ad copy when the landing page content is generic or doesn't contain specific promotional information."
        else:
            brief_context = ""

        # Format the template
        return USER_PROMPT_TEMPLATE.format(
            brand_instruction=brand_instruction,
            brand_inference_instruction=brand_inference_instruction,
            target_keyword=request.target_keyword,
            headline_1_type=request.headline_1_type,
            url=landing_page.url,
            title=landing_page.title or 'N/A',
            h1_tags=h1_tags,
            h2_tags=h2_tags,
            body_preview=body_preview,
            brief_context=brief_context,
            headline_1_count=request.headline_1_count + 2,
            headline_2_count=request.headline_2_count + 2,
            headline_3_count=request.headline_3_count + 2,
            description_1_count=request.description_1_count + 2,
            description_2_count=request.description_2_count + 2
        )
