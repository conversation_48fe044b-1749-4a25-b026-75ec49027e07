"""Supabase logging service for tracking generations and evaluations."""
import os
from typing import Optional, Dict, Any
from datetime import datetime
import json
from supabase import create_client, Client
import logging

logger = logging.getLogger(__name__)


class SupabaseLogger:
    """Handles logging to Supabase for observability."""
    
    def __init__(self) -> None:
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_KEY")
        
        if not url or not key:
            logger.warning("Supabase credentials not found. Logging disabled.")
            self.client = None
        else:
            self.client: Client = create_client(url, key)
    
    def log_generation(
        self,
        generation_id: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        model: str,
        duration_ms: float,
        error: Optional[str] = None
    ) -> bool:
        """Log a generation event to Supabase."""
        if not self.client:
            return False
            
        try:
            data = {
                "generation_id": generation_id,
                "request": json.dumps(request_data),
                "response": json.dumps(response_data),
                "model": model,
                "duration_ms": duration_ms,
                "error": error,
                "created_at": datetime.now().isoformat()
            }
            
            self.client.table("ad_copy_generations").insert(data).execute()
            return True
            
        except Exception as e:
            logger.error(f"Failed to log generation: {e}")
            return False
    
    def log_evaluation(
        self,
        generation_id: str,
        variant_index: int,
        rating: int,
        feedback: Optional[str] = None
    ) -> bool:
        """Log user evaluation/feedback for a variant."""
        if not self.client:
            return False
            
        try:
            data = {
                "generation_id": generation_id,
                "variant_index": variant_index,
                "rating": rating,
                "feedback": feedback,
                "created_at": datetime.now().isoformat()
            }
            
            self.client.table("ad_copy_evaluations").insert(data).execute()
            return True
            
        except Exception as e:
            logger.error(f"Failed to log evaluation: {e}")
            return False