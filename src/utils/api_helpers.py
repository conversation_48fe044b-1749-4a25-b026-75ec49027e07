"""
API helper utilities for retry and rate limiting mechanisms.
"""
import time
import functools
from typing import Callable, Any, Optional, TypeVar, cast
from datetime import datetime, timedelta
import asyncio
from collections import deque
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')


class RateLimiter:
    """Token bucket rate limiter for API calls."""
    
    def __init__(self, calls_per_minute: int = 10):
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute  # seconds between calls
        self.last_call_time = 0.0
        self.call_times = deque(maxlen=calls_per_minute)
    
    def wait_if_needed(self) -> None:
        """Wait if necessary to respect rate limit."""
        current_time = time.time()
        
        # Remove calls older than 1 minute
        while self.call_times and self.call_times[0] < current_time - 60:
            self.call_times.popleft()
        
        # If we've made too many calls in the last minute, wait
        if len(self.call_times) >= self.calls_per_minute:
            oldest_call = self.call_times[0]
            wait_time = 60 - (current_time - oldest_call) + 0.1  # Add small buffer
            if wait_time > 0:
                logger.info(f"Rate limit reached. Waiting {wait_time:.1f} seconds...")
                time.sleep(wait_time)
        
        # Record this call
        self.call_times.append(time.time())


def retry_with_exponential_backoff(
    max_retries: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """
    Decorator for retrying functions with exponential backoff.
    
    Args:
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay in seconds
        max_delay: Maximum delay in seconds
        exponential_base: Base for exponential backoff
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Max retries ({max_retries}) reached for {func.__name__}")
                        raise
                    
                    # Calculate delay with exponential backoff
                    delay = min(initial_delay * (exponential_base ** attempt), max_delay)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}: {str(e)}. "
                        f"Retrying in {delay:.1f} seconds..."
                    )
                    
                    time.sleep(delay)
            
            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
            raise RuntimeError(f"Unexpected error in retry logic for {func.__name__}")
        
        return cast(Callable[..., T], wrapper)
    
    return decorator


def rate_limited(rate_limiter: RateLimiter) -> Callable:
    """
    Decorator to apply rate limiting to a function.
    
    Args:
        rate_limiter: RateLimiter instance to use
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            rate_limiter.wait_if_needed()
            return func(*args, **kwargs)
        
        return cast(Callable[..., T], wrapper)
    
    return decorator


# Async versions for async functions
class AsyncRateLimiter:
    """Async token bucket rate limiter for API calls."""
    
    def __init__(self, calls_per_minute: int = 10):
        self.calls_per_minute = calls_per_minute
        self.min_interval = 60.0 / calls_per_minute
        self.call_times = deque(maxlen=calls_per_minute)
        self._lock = asyncio.Lock()
    
    async def wait_if_needed(self) -> None:
        """Wait if necessary to respect rate limit."""
        async with self._lock:
            current_time = time.time()
            
            # Remove calls older than 1 minute
            while self.call_times and self.call_times[0] < current_time - 60:
                self.call_times.popleft()
            
            # If we've made too many calls in the last minute, wait
            if len(self.call_times) >= self.calls_per_minute:
                oldest_call = self.call_times[0]
                wait_time = 60 - (current_time - oldest_call) + 0.1
                if wait_time > 0:
                    logger.info(f"Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
            
            # Record this call
            self.call_times.append(time.time())


def async_retry_with_exponential_backoff(
    max_retries: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """Async version of retry with exponential backoff."""
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Max retries ({max_retries}) reached for {func.__name__}")
                        raise
                    
                    delay = min(initial_delay * (exponential_base ** attempt), max_delay)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}: {str(e)}. "
                        f"Retrying in {delay:.1f} seconds..."
                    )
                    
                    await asyncio.sleep(delay)
            
            if last_exception:
                raise last_exception
            raise RuntimeError(f"Unexpected error in retry logic for {func.__name__}")
        
        return cast(Callable[..., T], wrapper)
    
    return decorator


# Example usage patterns
if __name__ == "__main__":
    # Example 1: Simple retry decorator
    @retry_with_exponential_backoff(max_retries=3, initial_delay=1.0)
    def unreliable_api_call():
        """Simulated API call that might fail."""
        import random
        if random.random() < 0.7:  # 70% chance of failure
            raise Exception("API call failed")
        return "Success!"
    
    # Example 2: Rate limiting
    api_rate_limiter = RateLimiter(calls_per_minute=10)
    
    @rate_limited(api_rate_limiter)
    @retry_with_exponential_backoff(max_retries=2)
    def rate_limited_api_call(data: str) -> str:
        """API call with both rate limiting and retry."""
        # Your API call here
        return f"Processed: {data}"
    
    # Example 3: Combined usage
    print("Testing retry mechanism...")
    try:
        result = unreliable_api_call()
        print(f"Result: {result}")
    except Exception as e:
        print(f"Failed after all retries: {e}")
    
    print("\nTesting rate limiting...")
    for i in range(15):
        try:
            result = rate_limited_api_call(f"Request {i}")
            print(f"{datetime.now().strftime('%H:%M:%S')} - {result}")
        except Exception as e:
            print(f"Error: {e}")