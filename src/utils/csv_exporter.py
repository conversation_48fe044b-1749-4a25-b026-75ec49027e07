"""CSV export utilities."""
import pandas as pd
from typing import List, Dict
from io import BytesIO
from ..models.ad_copy import AdCopyGeneration


def export_to_csv(generation: AdCopyGeneration) -> BytesIO:
    """Export ad copy generation to CSV matching Rob's format."""
    try:
        # Convert to CSV format
        csv_data = generation.to_csv_format()
        
        # Create DataFrame
        df = pd.DataFrame(csv_data)
        
        # Ensure column order matches template
        column_order = [
            "Approved",
            "Live", 
            "Asset",
            "Asset type",
            "Impr. (Existing Copy)",
            "Character #",
            "Position pinning",
            "Notes",
            "Feedback"
        ]
        
        df = df[column_order]
        
        # Add header rows like in the sample
        header_data = {
            "Approved": ["Responsive Search Ad Assets", "", "", "", ""],
            "Live": ["", "", "", "", ""],
            "Asset": ["", "", "", f"Target Keyword: {generation.request.target_keyword}", ""],
            "Asset type": ["", "", "", "", ""],
            "Impr. (Existing Copy)": ["", "", "", "", ""],
            "Character #": ["", "", "", "", ""],
            "Position pinning": ["", "", "", "", ""],
            "Notes": ["", "", "", "", ""],
            "Feedback": ["", "", "", "", ""]
        }
        
        header_df = pd.DataFrame(header_data)
        
        # Combine header and data
        final_df = pd.concat([header_df, df], ignore_index=True)
        
        # Write to BytesIO
        buffer = BytesIO()
        final_df.to_csv(buffer, index=False)
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        # Log the error but create a minimal CSV with error message
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error exporting to CSV: {str(e)}")
        
        # Create a minimal error CSV
        error_buffer = BytesIO()
        error_df = pd.DataFrame({
            "Error": [f"Failed to export: {str(e)}"],
            "Target Keyword": [generation.request.target_keyword if generation else "Unknown"],
            "Generated At": [str(generation.generated_at) if generation else "Unknown"]
        })
        error_df.to_csv(error_buffer, index=False)
        error_buffer.seek(0)
        
        return error_buffer


def create_summary_stats(generation: AdCopyGeneration) -> Dict[str, int]:
    """Create summary statistics for the generation."""
    try:
        stats = {
            "total_variants": len(generation.variants),
            "headlines": len([v for v in generation.variants if v.asset_type == "Headline"]),
            "descriptions": len([v for v in generation.variants if v.asset_type == "Description"]),
            "headline_1": len([v for v in generation.variants if v.position_pinning == "1st" and v.asset_type == "Headline"]),
            "headline_2": len([v for v in generation.variants if v.position_pinning == "2nd" and v.asset_type == "Headline"]),
            "headline_3": len([v for v in generation.variants if v.position_pinning == "3rd" and v.asset_type == "Headline"]),
            "description_1": len([v for v in generation.variants if v.position_pinning == "1st" and v.asset_type == "Description"]),
            "description_2": len([v for v in generation.variants if v.position_pinning == "2nd" and v.asset_type == "Description"])
        }
        return stats
    except Exception as e:
        # Return safe defaults if there's an error
        return {
            "total_variants": 0,
            "headlines": 0,
            "descriptions": 0,
            "headline_1": 0,
            "headline_2": 0,
            "headline_3": 0,
            "description_1": 0,
            "description_2": 0
        }