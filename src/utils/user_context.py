"""
User context management for database logging.
Uses contextvars to track the current authenticated user across the application.
"""
import contextvars
from typing import Optional

# Context variable to store the current authenticated user
current_user: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('current_user', default=None)

def set_current_user(username: Optional[str]) -> None:
    """Set the current authenticated user in context."""
    current_user.set(username)

def get_current_user() -> Optional[str]:
    """Get the current authenticated user from context."""
    return current_user.get()

def clear_current_user() -> None:
    """Clear the current user from context (e.g., on logout)."""
    current_user.set(None)