"""
Shared database logging utility for all components.
Can be imported from anywhere in the project.
"""
import os
from typing import Optional, Union
from supabase import create_client, Client
from pydantic import BaseModel
from .user_context import get_current_user

# Ensure environment variables are loaded
# This is safe to call multiple times
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # dotenv not available, assume env vars are set

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

if not supabase_url or not supabase_key:
    print("SUPABASE_URL and SUPABASE_KEY not found. Enhanced logging disabled.")
    supabase: Optional[Client] = None
else:
    supabase: Optional[Client] = create_client(supabase_url, supabase_key)


def log_generation(entry: Union[dict, BaseModel]) -> Optional[dict]:
    """
    Log a generation to Supabase

    Args:
        entry: Dictionary with generation data or GenerationLogEntry instance

    Returns:
        The inserted record or None if error
    """
    if not supabase:
        return None
        
    try:
        # If it's a Pydantic model, convert to dict
        if isinstance(entry, BaseModel):
            data = entry.model_dump(exclude_none=True)
        elif isinstance(entry, dict):
            data = entry.copy()
        else:
            raise ValueError("Entry must be a dict or BaseModel instance")

        # Convert UUID to string for JSON serialization
        if 'batch_id' in data:
            data['batch_id'] = str(data['batch_id'])

        # Add current user if available
        current_user = get_current_user()
        if current_user:
            data['created_by'] = current_user

        # Insert into Supabase
        result = supabase.table('ad_generation_logs').insert(data).execute()

        return result.data[0] if result.data else None

    except Exception as e:
        print(f"Error logging to Supabase: {e}")
        return None


def get_generations_by_batch(batch_id: str) -> list:
    """Get all variants from a batch"""
    if not supabase:
        return []
        
    try:
        result = supabase.table('ad_generation_logs') \
            .select("*") \
            .eq('batch_id', batch_id) \
            .order('variant_number') \
            .execute()
        return result.data
    except Exception as e:
        print(f"Error fetching from Supabase: {e}")
        return []


def submit_feedback(generation_id: str, production_readiness: str, feedback_text: Optional[str]) -> Optional[dict]:
    """Submit user feedback for a generation"""
    if not supabase:
        return None
        
    try:
        result = supabase.table('ad_generation_logs') \
            .update({
                'production_readiness': production_readiness,
                'feedback_text': feedback_text,
                'evaluated_at': 'now()'
            }) \
            .eq('generation_id', generation_id) \
            .execute()
        return result.data[0] if result.data else None
    except Exception as e:
        print(f"Error updating evaluation: {e}")
        return None