"""Google Sheets export utilities."""
import gspread
from google.oauth2.service_account import Credentials
from gspread_formatting import DataValidationRule, BooleanCondition, set_data_validation_for_cell_range
import pandas as pd
from typing import Optional, List, Dict
import logging
from datetime import datetime
from ..models.ad_copy import AdCopyGeneration

logger = logging.getLogger(__name__)


class GoogleSheetsExporter:
    """Export ad copy to Google Sheets."""
    
    def __init__(self, credentials_path: str):
        """Initialize with service account credentials."""
        self.credentials_path = credentials_path
        self.client: Optional[gspread.Client] = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Sheets API."""
        try:
            # Define the required scopes
            scopes = [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive'
            ]
            
            # Load credentials
            creds = Credentials.from_service_account_file(
                self.credentials_path,
                scopes=scopes
            )
            
            # Authorize the client
            self.client = gspread.authorize(creds)
            logger.info("Successfully authenticated with Google Sheets API")
            
        except Exception as e:
            logger.error(f"Failed to authenticate: {str(e)}")
            raise
    
    def export_to_sheet(self, generation: AdCopyGeneration, sheet_url: str, 
                       campaign_name: Optional[str] = None, ad_group_name: Optional[str] = None,
                       date_range: Optional[str] = None) -> bool:
        """Export ad copy generation to Google Sheet matching client's exact format."""
        try:
            # Ensure client is authenticated
            if not self.client:
                logger.error("Google Sheets client not authenticated")
                return False
                
            # Open the spreadsheet
            sheet = self.client.open_by_url(sheet_url)
            
            # Create new worksheet with date/time title
            worksheet_title = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            worksheet = sheet.add_worksheet(title=worksheet_title, rows=100, cols=20)
            
            # Convert to CSV format (reuse existing logic)
            csv_data = generation.to_csv_format()
            
            # Create DataFrame
            df = pd.DataFrame(csv_data)
            
            # Ensure column order matches template
            column_order = [
                "Approved",
                "Live", 
                "Asset",
                "Asset type",
                "Impr. (Existing Copy)",
                "Character #",
                "Position pinning",
                "Notes",
                "Feedback"
            ]
            
            df = df[column_order]
            
            # Create metadata rows matching client format EXACTLY
            metadata_rows = [
                ["Responsive Search Ad Assets ", "", "", "", "", "", "", "", ""],
                ["Campaign(s)", campaign_name or "Your Campaign Name", "", "", "", "", "", "", ""],
                ["Ad Group(s)", ad_group_name or "Your Ad Group Name", "", "", "", "", "", "", ""],
                ["Landing Page", generation.request.landing_page_url, "", "", "", "", "", "", ""],
                ["", "", "", "", date_range or "(Generated via AI Tool)", "", "", "", ""]
            ]
            
            # Add column headers
            metadata_rows.append(column_order)
            
            # Convert DataFrame values - use False for unchecked checkboxes
            data_rows = []
            for _, row in df.iterrows():
                new_row = [
                    False,  # Approved - will become unchecked checkbox
                    False,  # Live - will become unchecked checkbox
                    row["Asset"],
                    row["Asset type"],
                    "",  # Impr. (Existing Copy) - leave empty for new
                    str(row["Character #"]),
                    row["Position pinning"],
                    "New",  # Notes - mark all as new
                    ""  # Feedback - empty
                ]
                data_rows.append(new_row)
            
            # Combine all rows
            all_rows = metadata_rows + data_rows
            
            # Update the new worksheet with all data
            worksheet.update(all_rows, range_name='A1')
            
            # Format the sheet
            self._format_sheet(worksheet, len(all_rows))
            
            logger.info(f"Successfully exported to Google Sheet: {sheet_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export to Google Sheets: {str(e)}")
            return False
    
    def _format_sheet(self, worksheet, num_rows: int):
        """Apply formatting to the sheet."""
        try:
            # Bold the header row
            worksheet.format('A6:I6', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9}
            })
            
            # Add checkboxes to Approved and Live columns (columns A and B)
            # Skip the header rows (rows 1-6), apply to data rows only
            if num_rows > 6:
                # Create checkbox validation rule
                checkbox_rule = DataValidationRule(
                    BooleanCondition(type='BOOLEAN'),
                    showCustomUi=True
                )
                
                # Apply to Approved column (A)
                set_data_validation_for_cell_range(
                    worksheet, 
                    f'A7:A{num_rows}', 
                    checkbox_rule
                )
                
                # Apply to Live column (B)
                set_data_validation_for_cell_range(
                    worksheet, 
                    f'B7:B{num_rows}', 
                    checkbox_rule
                )
                
                logger.info("Added checkboxes to Approved and Live columns")
            
            # Set specific column widths to match client format
            try:
                # Define column widths
                column_widths = [
                    140,   # A: Approved (checkbox)
                    120,   # B: Live (checkbox) 
                    600,  # C: Asset (much wider for copy)
                    100,  # D: Asset type
                    180,  # E: Impr. (Existing Copy) - wider
                    120,   # F: Character #
                    140,  # G: Position pinning (wider!)
                    150,  # H: Notes
                    150   # I: Feedback
                ]
                
                # Create batch update requests
                requests = []
                for col_index, width in enumerate(column_widths):
                    requests.append({
                        "updateDimensionProperties": {
                            "range": {
                                "sheetId": worksheet.id,
                                "dimension": "COLUMNS",
                                "startIndex": col_index,
                                "endIndex": col_index + 1
                            },
                            "properties": {
                                "pixelSize": width
                            },
                            "fields": "pixelSize"
                        }
                    })
                
                # Apply column width updates
                worksheet.spreadsheet.batch_update({"requests": requests})
                
                # Center align specific columns (D, E, F = Asset type, Impr., Character #)
                # Columns are 0-indexed, so D=3, E=4, F=5
                center_ranges = ['D7:D', 'E7:E', 'F7:F']
                for range_str in center_ranges:
                    worksheet.format(f"{range_str}{num_rows}", {
                        'horizontalAlignment': 'CENTER'
                    })
                
            except Exception as e:
                logger.warning(f"Failed to set column widths: {str(e)}")
                
        except Exception as e:
            logger.warning(f"Failed to apply formatting: {str(e)}")
            # Continue even if formatting fails


def create_demo_sheet(credentials_path: str, sheet_url: str, sample_data: Optional[AdCopyGeneration] = None):
    """Create a demo sheet with sample data."""
    try:
        exporter = GoogleSheetsExporter(credentials_path)
        
        if sample_data:
            # Use provided sample data
            return exporter.export_to_sheet(sample_data, sheet_url)
        else:
            # Create minimal demo data
            if not exporter.client:
                logger.error("Google Sheets client not authenticated")
                return False
            client = exporter.client
            sheet = client.open_by_url(sheet_url)
            worksheet = sheet.get_worksheet(0)
            
            # Add demo headers
            demo_data = [
                ["Responsive Search Ad Assets - DEMO", "", "", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", ""],
                ["This is a demo showing Google Sheets integration", "", "", "", "", "", "", "", ""],
                ["", "", "Target Keyword: [Your Keyword Here]", "", "", "", "", "", ""],
                ["", "", "", "", "", "", "", "", ""],
                ["Approved", "Live", "Asset", "Asset type", "Impr. (Existing Copy)", "Character #", "Position pinning", "Notes", "Feedback"],
                ["", "", "Sample headline here", "Headline", "", "20", "1st", "", ""],
                ["", "", "Another sample headline", "Headline", "", "23", "", "", ""],
                ["", "", "Sample description text goes here", "Description", "", "34", "1st", "", ""]
            ]
            
            worksheet.update(demo_data, range_name='A1')
            logger.info("Created demo sheet successfully")
            return True
            
    except Exception as e:
        logger.error(f"Failed to create demo sheet: {str(e)}")
        return False