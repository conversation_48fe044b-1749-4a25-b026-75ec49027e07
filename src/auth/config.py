import os
import streamlit_authenticator as stauth
from streamlit_authenticator.utilities.hasher import Hasher
from dotenv import load_dotenv

load_dotenv()

def get_authenticator():
    """Configure and return the authenticator object."""
    
    # Get credentials from environment variables
    usernames = os.getenv("AUTH_USERNAMES", "admin").split(",")
    passwords = os.getenv("AUTH_PASSWORDS", "").split(",")
    names = os.getenv("AUTH_NAMES", "Admin User").split(",")
    
    if not passwords or passwords == [""]:
        raise ValueError("AUTH_PASSWORDS environment variable must be set")
    
    if len(usernames) != len(passwords) or len(usernames) != len(names):
        raise ValueError("AUTH_USERNAMES, AUTH_PASSWORDS, and AUTH_NAMES must have the same number of comma-separated values")
    
    # Hash passwords
    hasher = Hasher()
    hashed_passwords = hasher.hash_list(passwords)
    
    # Create credentials dictionary
    credentials = {
        "usernames": {}
    }
    
    for username, name, hashed_pw in zip(usernames, names, hashed_passwords):
        credentials["usernames"][username.strip()] = {
            "name": name.strip(),
            "password": hashed_pw
        }
    
    # Create authenticator object
    authenticator = stauth.Authenticate(
        credentials=credentials,
        cookie_name=os.getenv("AUTH_COOKIE_NAME", "sem_ad_copy_auth"),
        cookie_key=os.getenv("AUTH_COOKIE_KEY", "sem_ad_copy_secret_key_2024"),
        cookie_expiry_days=int(os.getenv("AUTH_COOKIE_EXPIRY_DAYS", "30"))
    )
    
    return authenticator