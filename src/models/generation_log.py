"""Pydantic models for generation logging."""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import uuid


class GenerationLogEntry(BaseModel):
    """Database log entry for ad generation"""
    # Core fields
    generation_id: str
    batch_id: uuid.UUID
    variant_number: int
    
    # Request data
    brand: str
    target_keyword: str
    headline_1_type: str
    headline_1_count: int
    headline_2_count: int
    headline_3_count: int
    description_1_count: int
    description_2_count: int
    
    # Landing page data
    landing_page_url: str
    landing_page_title: Optional[str] = None
    landing_page_h1_tags: Optional[List[str]] = None
    landing_page_h2_tags: Optional[List[str]] = None
    landing_page_body_preview: Optional[str] = None
    
    # System data
    system_version: str
    model_name: str
    status: str = "success"
    error_message: Optional[str] = None
    
    # Prompt data
    system_prompt: str
    user_prompt: str
    
    # API parameters
    temperature: float
    max_tokens: int
    top_p: float
    
    # API trace
    api_request: dict
    api_response: Optional[dict] = None
    
    # Generated content
    generated_headlines_p1: Optional[List[Dict[str, Any]]] = None
    generated_headlines_p2: Optional[List[Dict[str, Any]]] = None
    generated_headlines_p3: Optional[List[Dict[str, Any]]] = None
    generated_descriptions_p1: Optional[List[Dict[str, Any]]] = None
    generated_descriptions_p2: Optional[List[Dict[str, Any]]] = None
    full_variant_data: Optional[dict] = None
    
    # Usage metrics
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    latency_ms: Optional[int] = None