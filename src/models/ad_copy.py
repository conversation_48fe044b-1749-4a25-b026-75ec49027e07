"""Data models for ad copy generation."""
from __future__ import annotations
from typing import List, Optional, Literal, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime


class HeadlineType(BaseModel):
    """Types of headlines by position."""
    position_1: Literal["brand_mention", "product_mention", "promo_messaging"]
    position_2: List[Literal["value_props", "urgency", "immediacy"]]
    position_3: List[Literal["cta"]]


class AdCopyVariant(BaseModel):
    """Single ad copy variant with metadata."""
    asset: str
    asset_type: Literal["Headline", "Description"]
    position_pinning: Literal["1st", "2nd", "3rd"]  # position
    copy_type: str  # e.g., "brand_mention", "urgency", etc.
    character_count: int


class AdCopyRequest(BaseModel):
    """Request model for ad copy generation."""
    landing_page_url: str
    target_keyword: str
    headline_1_type: Literal["brand_mention", "product_mention", "promo_messaging"]
    
    # Quantity specifications
    headline_1_count: int = Field(default=7, ge=1, le=15)
    headline_2_count: int = Field(default=5, ge=1, le=15)
    headline_3_count: int = Field(default=3, ge=1, le=15)
    description_1_count: int = Field(default=3, ge=1, le=4)
    description_2_count: int = Field(default=2, ge=1, le=4)
    
    # Optional brand name - if not provided, AI will infer from landing page
    brand: Optional[str] = None

    # Optional brief/context - additional context when landing page is generic or non-promotional
    brief: Optional[str] = Field(default=None, description="Additional context or brief when landing page doesn't contain specific promotional information")


class AdCopyGeneration(BaseModel):
    """Complete generation result."""
    request: AdCopyRequest
    landing_page_content: LandingPageContent
    variants: List[AdCopyVariant]
    generated_at: datetime = Field(default_factory=datetime.now)
    generation_id: Optional[str] = None

    model_config = {"arbitrary_types_allowed": True}
    
    def to_csv_format(self) -> List[dict]:
        """Convert to CSV format matching Rob's template."""
        csv_rows = []
        for variant in self.variants:
            csv_rows.append({
                "Approved": "TRUE",
                "Live": "TRUE", 
                "Asset": variant.asset,
                "Asset type": variant.asset_type,
                "Impr. (Existing Copy)": "",
                "Character #": variant.character_count,
                "Position pinning": variant.position_pinning,
                "Notes": "New",
                "Feedback": ""
            })
        return csv_rows


class LandingPageContent(BaseModel):
    """Extracted content from landing page."""
    url: str
    title: Optional[str]
    meta_description: Optional[str]
    h1_tags: List[str] = []
    h2_tags: List[str] = []
    h3_tags: List[str] = []
    body_text: Optional[str]
    extracted_at: datetime = Field(default_factory=datetime.now)
