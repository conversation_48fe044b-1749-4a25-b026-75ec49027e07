"""JSON schemas for structured ad copy generation."""
from typing import Dict, List
from pydantic import BaseModel, Field, create_model
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def create_ad_copy_json_schema(
    headline_1_count: int,
    headline_2_count: int, 
    headline_3_count: int,
    description_1_count: int,
    description_2_count: int
) -> Dict:
    """Create dynamic JSON schema for ad copy generation."""
    
    return {
        "type": "object",
        "properties": {
            "target_keyword": {"type": "string"},
            "brand": {"type": "string"},
            "headlines": {
                "type": "object",
                "properties": {
                    "position_1": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "maxLength": 30},
                                "copy_type": {"type": "string"},
                                "character_count": {"type": "integer"}
                            },
                            "required": ["text", "copy_type", "character_count"]
                        },
                        "minItems": headline_1_count + 2,
                        "maxItems": headline_1_count + 2
                    },
                    "position_2": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "maxLength": 30},
                                "copy_type": {"type": "string", "enum": ["value_props", "urgency", "immediacy"]},
                                "character_count": {"type": "integer"}
                            },
                            "required": ["text", "copy_type", "character_count"]
                        },
                        "minItems": headline_2_count + 2,
                        "maxItems": headline_2_count + 2
                    },
                    "position_3": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "maxLength": 30},
                                "copy_type": {"type": "string", "enum": ["cta"]},
                                "character_count": {"type": "integer"}
                            },
                            "required": ["text", "copy_type", "character_count"]
                        },
                        "minItems": headline_3_count + 2,
                        "maxItems": headline_3_count + 2
                    }
                },
                "required": ["position_1", "position_2", "position_3"]
            },
            "descriptions": {
                "type": "object",
                "properties": {
                    "position_1": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "maxLength": 90},
                                "copy_type": {"type": "string", "enum": ["value_props"]},
                                "character_count": {"type": "integer"}
                            },
                            "required": ["text", "copy_type", "character_count"]
                        },
                        "minItems": description_1_count + 2,
                        "maxItems": description_1_count + 2
                    },
                    "position_2": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "text": {"type": "string", "maxLength": 90},
                                "copy_type": {"type": "string", "enum": ["product_brand_cta"]},
                                "character_count": {"type": "integer"},
                                "cta": {"type": "string"}
                            },
                            "required": ["text", "copy_type", "character_count", "cta"]
                        },
                        "minItems": description_2_count + 2,
                        "maxItems": description_2_count + 2
                    }
                },
                "required": ["position_1", "position_2"]
            }
        },
        "required": ["target_keyword", "brand", "headlines", "descriptions"]
    }


class HeadlineItem(BaseModel):
    """Single headline item."""
    text: str
    copy_type: str
    character_count: int


class DescriptionItem(BaseModel):
    """Single description item."""
    text: str
    copy_type: str
    character_count: int
    cta: str = Field(default="", description="Call to action for position 2")


class Headlines(BaseModel):
    """All headlines organized by position."""
    position_1: List[HeadlineItem]
    position_2: List[HeadlineItem]
    position_3: List[HeadlineItem]


class Descriptions(BaseModel):
    """All descriptions organized by position."""
    position_1: List[DescriptionItem]
    position_2: List[DescriptionItem]


class StructuredAdCopyGeneration(BaseModel):
    """Structured output for ad copy generation."""
    target_keyword: str
    brand: str
    headlines: Headlines
    descriptions: Descriptions
    
    def to_variants(self, requested_counts: Dict[str, int]) -> List[Dict]:
        """Convert structured output to list of variants with filtering.
        
        Filters out variants that exceed character limits and returns only
        the requested number of valid variants.
        """
        variants = []
        
        # Process headlines - filter out any over 30 chars
        position_configs = [
            ("1st", self.headlines.position_1, requested_counts['headline_1_count'], 30),
            ("2nd", self.headlines.position_2, requested_counts['headline_2_count'], 30),
            ("3rd", self.headlines.position_3, requested_counts['headline_3_count'], 30),
        ]
        
        for position, items, requested_count, char_limit in position_configs:
            # Filter to keep only valid items (30 chars or less)
            valid_items = [item for item in items if len(item.text) <= char_limit]
            
            # Check if we have enough valid items
            if len(valid_items) < requested_count:
                shortfall = requested_count - len(valid_items)
                invalid_count = len(items) - len(valid_items)
                logger.warning(
                    f"Headline generation shortfall - Position: {position}, "
                    f"Requested: {requested_count}, Valid: {len(valid_items)}, "
                    f"Shortfall: {shortfall}, Invalid (>30 chars): {invalid_count}",
                    extra={
                        "asset_type": "Headline",
                        "position": position,
                        "requested_count": requested_count,
                        "valid_count": len(valid_items),
                        "shortfall": shortfall,
                        "invalid_count": invalid_count,
                        "char_limit": char_limit
                    }
                )
            
            # Take only the requested count from valid items
            for item in valid_items[:requested_count]:
                variants.append({
                    "asset": item.text,
                    "asset_type": "Headline",
                    "position_pinning": position,
                    "copy_type": item.copy_type,
                    "character_count": len(item.text)
                })
        
        # Process descriptions - filter out any over 90 chars
        desc_configs = [
            ("1st", self.descriptions.position_1, requested_counts['description_1_count'], 90),
            ("2nd", self.descriptions.position_2, requested_counts['description_2_count'], 90),
        ]
        
        for position, items, requested_count, char_limit in desc_configs:
            # Filter to keep only valid items (90 chars or less)
            valid_items = [item for item in items if len(item.text) <= char_limit]
            
            # Check if we have enough valid items
            if len(valid_items) < requested_count:
                shortfall = requested_count - len(valid_items)
                invalid_count = len(items) - len(valid_items)
                logger.warning(
                    f"Description generation shortfall - Position: {position}, "
                    f"Requested: {requested_count}, Valid: {len(valid_items)}, "
                    f"Shortfall: {shortfall}, Invalid (>90 chars): {invalid_count}",
                    extra={
                        "asset_type": "Description",
                        "position": position,
                        "requested_count": requested_count,
                        "valid_count": len(valid_items),
                        "shortfall": shortfall,
                        "invalid_count": invalid_count,
                        "char_limit": char_limit
                    }
                )
            
            # Take only the requested count from valid items
            for item in valid_items[:requested_count]:
                variants.append({
                    "asset": item.text,
                    "asset_type": "Description",
                    "position_pinning": position,
                    "copy_type": item.copy_type,
                    "character_count": len(item.text)
                })
        
        return variants