"""Streamlit app for SEM ad copy generation."""
import os
import sys

from enum import Enum
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import streamlit as st
import pandas as pd
from datetime import datetime
import logging
from typing import TypedDict
from src.models.ad_copy import AdCopyRequest
from src.services.scraper import LandingPageScraper
from src.services.ad_generator import AdCopyGenerator
from src.utils.csv_exporter import export_to_csv, create_summary_stats
from src.utils.sheets_exporter import GoogleSheetsExporter
from src.utils.database_logging_util import submit_feedback
from src.utils.user_context import set_current_user, clear_current_user
from src.auth.config import get_authenticator
import sentry_sdk
from sentry_sdk.integrations.anthropic import AnthropicIntegration

from dotenv import load_dotenv

load_dotenv()

# Initialize Sentry
sentry_sdk.init(
    dsn=os.getenv("SENTRY_DSN"),
    integrations=[
        AnthropicIntegration(),
    ],
    traces_sample_rate=0.1,
    profiles_sample_rate=0.1,
    environment=os.getenv("ENVIRONMENT", "production"),
    release=os.getenv("APP_VERSION", "1.0.0"),
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Type definitions
class Services(TypedDict):
    scraper: LandingPageScraper
    generator: AdCopyGenerator

class HeadlineStrategy(str, Enum):
    BRAND_MENTION = "brand_mention"
    PRODUCT_MENTION = "product_mention"
    PROMO_MESSAGING = "promo_messaging"

# Page config
st.set_page_config(
    page_title="SEM Ad Copy Generator",
    page_icon="favicon.ico",
    layout="wide"
)

# Initialize services
@st.cache_resource
def get_services() -> Services:
    
    # Verify API key is available
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if api_key:
        logger.info("ANTHROPIC_API_KEY loaded successfully")
    else:
        logger.warning("ANTHROPIC_API_KEY not found during service initialization")
    
    return {
        "scraper": LandingPageScraper(),
        "generator": AdCopyGenerator()
    }

def main() -> None:
    # Authentication
    try:
        authenticator = get_authenticator()
    except ValueError as e:
        st.error(f"Authentication configuration error: {str(e)}")
        st.stop()
    
    try:
        authenticator.login()
    except Exception as e:
        st.error(e)
        st.stop()
    
    if st.session_state.get('authentication_status'):
        # Set the current user in context for database logging
        username = st.session_state.get('username')
        if username:
            set_current_user(username)
            # Also set user context in Sentry
            sentry_sdk.set_user({"username": username})
        
        # Handle logout
        if authenticator.logout():
            clear_current_user()
        # st.sidebar.write(f'Welcome *{st.session_state.get("name")}*')
        # st.sidebar.divider()
    elif st.session_state.get('authentication_status') is False:
        st.error('Username/password is incorrect')
        st.stop()
    elif st.session_state.get('authentication_status') is None:
        st.warning('Please enter your username and password')
        st.stop()
    
    st.title("SEM Ad Copy Generator")
    st.markdown("Generate responsive search ads optimized for Google Ads")
    
    # Initialize session state
    if "generation" not in st.session_state:
        st.session_state.generation = None
    if "show_sheets_export" not in st.session_state:
        st.session_state.show_sheets_export = False
    
    # Sidebar for inputs
    with st.sidebar:
        st.header("Campaign Details")
        
        # Basic inputs
        ui_brand_name = st.text_input(
            "Brand Name (Optional)",
            placeholder="Leave blank to auto-detect from landing page",
            help="Specify brand name or leave empty to infer from landing page"
        )
        
        ui_target_keyword = st.text_input(
            "Target Keyword",
            placeholder="e.g., delay your period",
            help="Main keyword to optimize for"
        )
        
        ui_landing_page_url = st.text_input(
            "Landing Page URL",
            placeholder="https://example.com/product",
            help="The landing page URL for the ad"
        )

        ui_brief = st.text_area(
            "Brief/Context (Optional)",
            placeholder="Additional context when landing page is generic or non-promotional...",
            help="Provide additional context about your campaign, especially useful when the landing page doesn't contain specific promotional information or when the final landing page isn't ready yet.",
            height=100
        )

        st.divider()
        
        # Headline 1 type selection
        st.subheader("Headline Strategy")
        ui_headline_1_type = st.selectbox(
            "Headline Position 1 Type",
            options=[
                (HeadlineStrategy.BRAND_MENTION, "Brand Mention"),
                (HeadlineStrategy.PRODUCT_MENTION, "Product Mention"),
                (HeadlineStrategy.PROMO_MESSAGING, "Promo Messaging")
            ],
            format_func=lambda x: x[1],
            help="Sets the theme for all headlines in the ad set"
        )[0]
        
        st.divider()
        
        # Quantity controls
        st.subheader("Quantity Settings")
        st.info("Google Ads allows up to 15 headlines and 4 descriptions per RSA. Recommended: 7-5-3 split for headlines.")
        
        col1, col2 = st.columns(2)
        
        with col1:
            ui_headline_1_count = st.number_input(
                "Headlines Position 1",
                min_value=1,
                max_value=15,
                value=7,
                help="Theme-setting headlines (Brand/Product/Promo)"
            )
            
            ui_headline_2_count = st.number_input(
                "Headlines Position 2",
                min_value=1,
                max_value=15,
                value=5,
                help="USPs, Urgency, or Immediacy messaging"
            )
            
            ui_headline_3_count = st.number_input(
                "Headlines Position 3",
                min_value=1,
                max_value=15,
                value=3,
                help="Call-to-action headlines only"
            )
        
        with col2:
            ui_description_1_count = st.number_input(
                "Descriptions Position 1",
                min_value=1,
                max_value=4,
                value=3,
                help="USPs and Value Propositions"
            )
            
            ui_description_2_count = st.number_input(
                "Descriptions Position 2",
                min_value=1,
                max_value=4,
                value=2,
                help="Product/Brand mention + CTA ending"
            )
        
        st.divider()
        
        # Generate button
        generate_button = st.button(
            "Generate Ad Copy",
            type="primary",
            use_container_width=True,
            disabled=not (ui_landing_page_url and ui_target_keyword)
        )
    
    # Main content area
    if generate_button:
        services = get_services()
        
        # Validate URL first
        if not services["scraper"].validate_url(ui_landing_page_url):
            st.error("Invalid URL format. Please enter a valid HTTP/HTTPS URL.")
            return
        
        with st.spinner("Analyzing landing page..."):
            landing_page = services["scraper"].extract_content(ui_landing_page_url)
            
            if not landing_page:
                st.error("Failed to fetch landing page. Please check the URL and try again.")
                return
        
        with st.spinner("Generating ad copy..."):
            request = AdCopyRequest(
                landing_page_url=ui_landing_page_url,
                target_keyword=ui_target_keyword,
                brand=ui_brand_name if ui_brand_name.strip() else None,  # Convert empty string to None
                brief=ui_brief if ui_brief.strip() else None,  # Convert empty string to None
                headline_1_type=ui_headline_1_type.value,
                headline_1_count=ui_headline_1_count,
                headline_2_count=ui_headline_2_count,
                headline_3_count=ui_headline_3_count,
                description_1_count=ui_description_1_count,
                description_2_count=ui_description_2_count
            )
            
            generation = services["generator"].generate(request, landing_page)
            
            if generation:
                st.session_state.generation = generation
                st.success("Ad copy generated successfully!")
            else:
                # Check if it's an API key issue
                if not os.getenv("ANTHROPIC_API_KEY"):
                    st.error("ANTHROPIC_API_KEY not found. Please add it to your .env file.")
                    st.info("Create a .env file in the project root with: ANTHROPIC_API_KEY=your_key_here")
                    # Clear the cache to force reload on next attempt
                    get_services.clear()
                else:
                    st.error("Failed to generate ad copy. Please try again.")
    
    # Display results
    if st.session_state.generation:
        generation = st.session_state.generation
        
        # Summary stats
        stats = create_summary_stats(generation)
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Variants", stats["total_variants"])
        with col2:
            st.metric("Headlines", stats["headlines"])
        with col3:
            st.metric("Descriptions", stats["descriptions"])
        
        # Export options
        st.divider()
        st.subheader("Export Options")
        
        col1, col2 = st.columns(2)
        
        with col1:
            csv_buffer = export_to_csv(generation)
            st.download_button(
                label="Download CSV",
                data=csv_buffer.getvalue(),
                file_name=f"ad_copy_{generation.request.target_keyword.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                use_container_width=True
            )
        
        with col2:
            if st.button("Export to Google Sheets", type="secondary", use_container_width=True, key="sheets_export_btn"):
                st.session_state.show_sheets_export = True
        
        # Google Sheets Export Form (shown when button clicked)
        if st.session_state.show_sheets_export:
            with st.container():
                st.markdown("### Export to Google Sheets")
                
                # Create form for Google Sheets export
                with st.form("google_sheets_export_form"):
                    # Pre-populate with environment variable if available
                    default_sheet_url = os.getenv("GOOGLE_SHEET_URL", "")
                    sheet_url = st.text_input(
                        "Google Sheet URL",
                        value=default_sheet_url,
                        placeholder="https://docs.google.com/spreadsheets/d/...",
                        help="URL of the Google Sheet to export to. Must be shared with the service account.",
                        key="sheet_url_input"
                    )
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        campaign_name = st.text_input(
                            "Campaign Name",
                            value="Your Campaign Name",
                            help="Campaign name for the export",
                            key="campaign_name_input"
                        )
                    with col2:
                        ad_group_name = st.text_input(
                            "Ad Group Name", 
                            value="Your Ad Group Name",
                            help="Ad group name for the export",
                            key="ad_group_input"
                        )
                    
                    date_range = st.text_input(
                        "Date Range",
                        value="(Generated via AI Tool)",
                        help="Optional date range for the export",
                        key="date_range_input"
                    )
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        submit_button = st.form_submit_button("Export Now", type="primary", use_container_width=True)
                    with col2:
                        if st.form_submit_button("Cancel", use_container_width=True):
                            st.session_state.show_sheets_export = False
                            st.rerun()
                    
                    if submit_button and sheet_url:
                        try:
                            # Initialize exporter with credentials from environment
                            import tempfile
                            
                            service_account_json = os.getenv("GOOGLE_SERVICE_ACCOUNT_JSON")
                            
                            if not service_account_json:
                                st.error("Google Sheets credentials not configured.")
                                st.info("Contact your administrator to set up Google Sheets integration.")
                            else:
                                # Use credentials from environment variable
                                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                                    temp_file.write(service_account_json)
                                    creds_path = temp_file.name
                                
                                try:
                                    with st.spinner("Exporting to Google Sheets..."):
                                        exporter = GoogleSheetsExporter(creds_path)
                                        success = exporter.export_to_sheet(
                                            generation,
                                            sheet_url,
                                            campaign_name=campaign_name,
                                            ad_group_name=ad_group_name,
                                            date_range=date_range
                                        )
                                        
                                        if success:
                                            st.success("Successfully exported to Google Sheets!")
                                            st.info(f"A new worksheet tab has been created with timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                                            st.session_state.show_sheets_export = False
                                        else:
                                            st.error("Failed to export. Please check the sheet URL and permissions.")
                                finally:
                                    # Clean up temp file
                                    if 'creds_path' in locals():
                                        os.unlink(creds_path)
                        except Exception as e:
                            st.error(f"Export error: {str(e)}")
                            logger.error(f"Google Sheets export error: {e}", exc_info=True)
                    elif submit_button and not sheet_url:
                        st.warning("Please enter a Google Sheet URL")
        
        # Display variants in tabs
        tabs = st.tabs(["Headlines Position 1", "Headlines Position 2", "Headlines Position 3", 
                       "Descriptions Position 1", "Descriptions Position 2"])
        
        # Group variants by position and type
        position_groups = {
            "Headlines Position 1": [v for v in generation.variants if v.asset_type == "Headline" and v.position_pinning == "1st"],
            "Headlines Position 2": [v for v in generation.variants if v.asset_type == "Headline" and v.position_pinning == "2nd"],
            "Headlines Position 3": [v for v in generation.variants if v.asset_type == "Headline" and v.position_pinning == "3rd"],
            "Descriptions Position 1": [v for v in generation.variants if v.asset_type == "Description" and v.position_pinning == "1st"],
            "Descriptions Position 2": [v for v in generation.variants if v.asset_type == "Description" and v.position_pinning == "2nd"]
        }
        
        for tab, (position_name, variants) in zip(tabs, position_groups.items()):
            with tab:
                if variants:
                    df_data = []
                    for variant in variants:
                        df_data.append({
                            "Copy": variant.asset,
                            "Copy Type": variant.copy_type.replace("_", " ").title(),
                            "Characters": f"{variant.character_count}/{30 if 'Headline' in position_name else 90}",
                            "Within Limit": "✅" if (variant.character_count <= 30 if "Headline" in position_name else variant.character_count <= 90) else "❌"
                        })
                    
                    df = pd.DataFrame(df_data)
                    st.dataframe(df, use_container_width=True, hide_index=True)
                else:
                    st.info("No variants generated for this position")
        
        # Landing page context (collapsible)
        with st.expander("Landing Page Context"):
            col1, col2 = st.columns(2)
            with col1:
                st.write("**Title:**", generation.landing_page_content.title or "No title found")
                st.write("**Target Keyword:**", generation.request.target_keyword)
            with col2:
                st.write("**Headline 1 Type:**", generation.request.headline_1_type.replace("_", " ").title())
                st.write("**Generation ID:**", f"{generation.generation_id[:8]}..." if generation.generation_id else "N/A")
        
        # Feedback section
        st.divider()
        st.header("Feedback")
        st.markdown("Please provide useful feedback to help make output quality better.")
        
        # Initialize feedback state tied to the current generation
        current_gen_id = generation.generation_id if generation.generation_id else "unknown"
        feedback_key = f"feedback_submitted_{current_gen_id}"
        
        if feedback_key not in st.session_state:
            st.session_state[feedback_key] = False
        
        if not st.session_state[feedback_key]:
            col1, col2 = st.columns([1, 3])
            
            with col1:
                readiness = st.selectbox(
                    "Production Readiness",
                    options=[
                        ("", "Select..."),
                        ("ready", "✅ Ready to use"),
                        ("minor_edits", "📝 Needs minor edits"),
                        ("major_edits", "⚠️ Needs major edits"),
                        ("unusable", "❌ Unusable")
                    ],
                    format_func=lambda x: x[1],
                    help="How ready is this ad copy for production use?"
                )
            
            with col2:
                feedback_text = st.text_area(
                    "Additional Feedback (Optional)",
                    placeholder="What worked well? What could be improved? Any specific issues?",
                    height=100
                )
            
            if st.button("Submit Feedback", type="secondary", disabled=not readiness[0]):
                services = get_services()

                # Combine readiness and text feedback
                full_feedback = f"Readiness: {readiness[1]}"
                if feedback_text:
                    full_feedback += f"\n\nDetails: {feedback_text}"

                # Log feedback to enhanced logging system
                success = submit_feedback(
                    generation_id=generation.generation_id or "unknown",
                    production_readiness=readiness[0],
                    feedback_text=feedback_text if feedback_text else None
                )
                
                if success:
                    st.session_state[feedback_key] = True
                    st.rerun()
                else:
                    st.warning("Feedback saved locally (Supabase logging not configured)")
                    st.session_state[feedback_key] = True
                    st.rerun()
        else:
            st.success("✅ Thank you for your feedback! It helps us improve the tool.")
            if st.button("Submit Additional Feedback"):
                st.session_state[feedback_key] = False
                st.rerun()

if __name__ == "__main__":
    main()