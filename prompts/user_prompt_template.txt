Generate Responsive Search Ad (RSA) copy for:

{brand_instruction}Target Keyword: {target_keyword}
Headline 1 Type: {headline_1_type}

{brand_inference_instruction}

Landing Page Context:
- URL: {url}
- Title: {title}
- H1 Tags: {h1_tags}
- H2 Tags: {h2_tags}
- Key Content: {body_preview}

{brief_context}

Requirements:
1. All headlines ≤30 characters (including spaces)
2. All descriptions ≤90 characters (including spaces) 
3. Incorporate "{target_keyword}" concept naturally
4. Each variant must be unique
5. Descriptions must be single, complete sentences
6. Description 2 variants must end with a CTA

Generate:
- {headline_1_count} x Headline 1 variants ({headline_1_type} focused)
- {headline_2_count} x Headline 2 variants (mix of USPs/UVPs, urgency, immediacy)
- {headline_3_count} x Headline 3 variants (CTAs only)
- {description_1_count} x Description 1 variants (USPs/UVPs)
- {description_2_count} x Description 2 variants (product/brand mention + CTA ending)

For Headlines 2, create diverse options mixing value propositions, urgency, and immediacy messaging that complement the Headline 1 theme.

Return the output in the specified JSON format with accurate character counts.