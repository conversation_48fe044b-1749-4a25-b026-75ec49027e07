"""Comprehensive test suite for the web scraping functionality."""
import logging
import json
from typing import List, Dict, Any, Optional
from src.services.scraper import LandingPageScraper
from src.models.ad_copy import LandingPageContent
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ScraperTestSuite:
    """Test suite for validating scraping functionality across various websites."""
    
    def __init__(self):
        self.scraper = LandingPageScraper(timeout=30)
        self.test_results = []
        
    def test_url_both_methods(self, url: str, expected_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """Test a single URL with both trafilatura and BeautifulSoup methods."""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing URL: {url}")
        logger.info(f"{'='*60}")
        
        result = {
            "url": url,
            "trafilatura": self._test_single_method(url, "trafilatura", expected_fields),
            "beautifulsoup": self._test_single_method(url, "beautifulsoup", expected_fields),
            "comparison": {}
        }
        
        # Compare results between methods
        result["comparison"] = self._compare_methods(
            result["trafilatura"], 
            result["beautifulsoup"]
        )
        
        self.test_results.append(result)
        return result
        
    def _test_single_method(self, url: str, method: str, expected_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """Test a single extraction method."""
        logger.info(f"\n--- Testing with {method.upper()} ---")
        
        start_time = time.time()
        result = {
            "method": method,
            "status": "pending",
            "extraction_time": 0,
            "errors": [],
            "warnings": [],
            "content": None,
            "validation": {}
        }
        
        try:
            # Validate URL
            if not self.scraper.validate_url(url):
                result["status"] = "failed"
                result["errors"].append("Invalid URL format")
                return result
                
            # Extract content using specific method
            if method == "trafilatura":
                content = self.scraper._extract_with_trafilatura(url)
            else:
                content = self.scraper._extract_with_beautifulsoup(url)
                
            result["extraction_time"] = time.time() - start_time
            
            if not content:
                result["status"] = "failed"
                result["errors"].append(f"Failed to extract any content with {method}")
                return result
                
            result["content"] = content
            result["status"] = "success"
            
            # Validate extraction
            validation = self._validate_extraction(content, expected_fields)
            result["validation"] = validation
            
            # Check for warnings
            if validation["missing_fields"]:
                result["warnings"].append(f"Missing fields: {', '.join(validation['missing_fields'])}")
            if validation["empty_fields"]:
                result["warnings"].append(f"Empty fields: {', '.join(validation['empty_fields'])}")
                
            # Display results
            self._display_extraction_results(content, validation, method)
            
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Exception: {str(e)}")
            logger.error(f"Error testing {url} with {method}: {e}")
            
        return result
    
    def _validate_extraction(self, content: LandingPageContent, expected: Dict[str, Any] = None) -> Dict[str, Any]:
        """Validate extracted content."""
        validation = {
            "has_title": bool(content.title),
            "has_meta_description": bool(content.meta_description),
            "has_h1_tags": bool(content.h1_tags),
            "has_h2_tags": bool(content.h2_tags),
            "has_h3_tags": bool(content.h3_tags),
            "has_body_text": bool(content.body_text),
            "title_length": len(content.title) if content.title else 0,
            "meta_desc_length": len(content.meta_description) if content.meta_description else 0,
            "body_text_length": len(content.body_text) if content.body_text else 0,
            "h1_count": len(content.h1_tags),
            "h2_count": len(content.h2_tags),
            "h3_count": len(content.h3_tags),
            "missing_fields": [],
            "empty_fields": [],
            "character_encoding_issues": False
        }
        
        # Check for missing/empty fields
        if not content.title:
            validation["missing_fields"].append("title")
        if not content.meta_description:
            validation["missing_fields"].append("meta_description")
        if not content.h1_tags:
            validation["empty_fields"].append("h1_tags")
        if not content.body_text:
            validation["missing_fields"].append("body_text")
            
        # Check for character encoding issues
        all_text = " ".join([
            content.title or "",
            content.meta_description or "",
            " ".join(content.h1_tags),
            " ".join(content.h2_tags),
            content.body_text or ""
        ])
        
        # Common encoding issue indicators
        if any(char in all_text for char in ['�', '???', 'â€™', 'â€œ']):
            validation["character_encoding_issues"] = True
            
        # Validate against expected values if provided
        if expected:
            validation["expected_matches"] = {}
            for field, expected_value in expected.items():
                if hasattr(content, field):
                    actual_value = getattr(content, field)
                    if isinstance(expected_value, str):
                        validation["expected_matches"][field] = expected_value in str(actual_value)
                    else:
                        validation["expected_matches"][field] = actual_value == expected_value
                        
        return validation
    
    def _compare_methods(self, trafilatura_result: Dict[str, Any], beautifulsoup_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare results between the two extraction methods."""
        comparison = {
            "both_successful": trafilatura_result["status"] == "success" and beautifulsoup_result["status"] == "success",
            "speed_difference": abs(trafilatura_result["extraction_time"] - beautifulsoup_result["extraction_time"]),
            "faster_method": "trafilatura" if trafilatura_result["extraction_time"] < beautifulsoup_result["extraction_time"] else "beautifulsoup",
            "field_differences": {},
            "content_length_differences": {},
            "validation_differences": {}
        }
        
        if comparison["both_successful"]:
            traf_content = trafilatura_result["content"]
            bs_content = beautifulsoup_result["content"]
            
            # Compare field presence
            fields = ["title", "meta_description", "h1_tags", "h2_tags", "h3_tags", "body_text"]
            for field in fields:
                traf_val = getattr(traf_content, field)
                bs_val = getattr(bs_content, field)
                
                if field.endswith("_tags"):
                    # For list fields, compare counts
                    comparison["field_differences"][field] = {
                        "trafilatura_count": len(traf_val) if traf_val else 0,
                        "beautifulsoup_count": len(bs_val) if bs_val else 0,
                        "difference": len(traf_val) - len(bs_val) if traf_val and bs_val else None
                    }
                else:
                    # For text fields, compare presence and length
                    comparison["field_differences"][field] = {
                        "both_present": bool(traf_val) and bool(bs_val),
                        "only_in_trafilatura": bool(traf_val) and not bool(bs_val),
                        "only_in_beautifulsoup": not bool(traf_val) and bool(bs_val),
                        "identical": traf_val == bs_val if traf_val and bs_val else False
                    }
                    
                    if field == "body_text" and traf_val and bs_val:
                        comparison["content_length_differences"][field] = {
                            "trafilatura": len(traf_val),
                            "beautifulsoup": len(bs_val),
                            "difference": len(traf_val) - len(bs_val),
                            "ratio": len(traf_val) / len(bs_val) if len(bs_val) > 0 else 0
                        }
                        
            # Compare validation results
            for key in ["has_title", "has_meta_description", "has_h1_tags", "has_body_text"]:
                traf_val = trafilatura_result["validation"].get(key)
                bs_val = beautifulsoup_result["validation"].get(key)
                comparison["validation_differences"][key] = {
                    "trafilatura": traf_val,
                    "beautifulsoup": bs_val,
                    "agreement": traf_val == bs_val
                }
                
        return comparison
    
    def _display_extraction_results(self, content: LandingPageContent, validation: Dict[str, Any], method: str = ""):
        """Display extraction results in a readable format."""
        method_label = f" [{method.upper()}]" if method else ""
        logger.info(f"\n📊 EXTRACTION RESULTS{method_label}:")
        logger.info(f"  ✓ Title: {content.title[:60]}..." if content.title else "  ✗ Title: NOT FOUND")
        logger.info(f"  ✓ Meta Description: {content.meta_description[:60]}..." if content.meta_description else "  ✗ Meta Description: NOT FOUND")
        logger.info(f"  ✓ H1 Tags: {validation['h1_count']} found" if validation['h1_count'] > 0 else "  ✗ H1 Tags: NONE FOUND")
        logger.info(f"  ✓ H2 Tags: {validation['h2_count']} found" if validation['h2_count'] > 0 else "  ✗ H2 Tags: NONE FOUND")
        logger.info(f"  ✓ Body Text: {validation['body_text_length']} characters" if validation['body_text_length'] > 0 else "  ✗ Body Text: NOT FOUND")
        
        if validation.get('character_encoding_issues'):
            logger.warning("  ⚠️  Character encoding issues detected")
            
        if content.h1_tags:
            logger.info(f"\n  Sample H1s: {', '.join(content.h1_tags[:3])}")
        if content.h2_tags:
            logger.info(f"  Sample H2s: {', '.join(content.h2_tags[:3])}")
            
    def run_test_suite(self, test_urls: List[Dict[str, Any]]):
        """Run tests on multiple URLs."""
        logger.info("\n🚀 STARTING COMPREHENSIVE SCRAPER TEST SUITE")
        logger.info(f"Testing {len(test_urls)} URLs with both Trafilatura and BeautifulSoup...")
        
        for test_case in test_urls:
            url = test_case.get("url")
            expected = test_case.get("expected", {})
            result = self.test_url_both_methods(url, expected)
            
            # Display comparison results
            self._display_comparison_results(result)
            time.sleep(1)  # Be polite to servers
            
        # Generate summary report
        self._generate_summary_report()
        
    def _display_comparison_results(self, result: Dict[str, Any]):
        """Display comparison between the two methods."""
        comparison = result["comparison"]
        
        logger.info("\n🔍 METHOD COMPARISON:")
        logger.info(f"  Both methods successful: {comparison['both_successful']}")
        logger.info(f"  Faster method: {comparison['faster_method']} (by {comparison['speed_difference']:.2f}s)")
        
        if comparison["both_successful"]:
            logger.info("\n  Field Agreement:")
            for field, diff in comparison["field_differences"].items():
                if field.endswith("_tags"):
                    logger.info(f"    {field}: Trafilatura={diff['trafilatura_count']}, BeautifulSoup={diff['beautifulsoup_count']}")
                else:
                    if diff["identical"]:
                        logger.info(f"    {field}: ✅ Identical")
                    elif diff["both_present"]:
                        logger.info(f"    {field}: ⚠️  Both present but different")
                    elif diff["only_in_trafilatura"]:
                        logger.info(f"    {field}: 🔵 Only in Trafilatura")
                    elif diff["only_in_beautifulsoup"]:
                        logger.info(f"    {field}: 🟡 Only in BeautifulSoup")
                    else:
                        logger.info(f"    {field}: ❌ Missing in both")
                        
            if "body_text" in comparison["content_length_differences"]:
                body_diff = comparison["content_length_differences"]["body_text"]
                logger.info(f"\n  Body Text Length:")
                logger.info(f"    Trafilatura: {body_diff['trafilatura']} chars")
                logger.info(f"    BeautifulSoup: {body_diff['beautifulsoup']} chars")
                logger.info(f"    Ratio: {body_diff['ratio']:.2f}x")
        
    def _generate_summary_report(self):
        """Generate summary report of all tests."""
        logger.info("\n" + "="*60)
        logger.info("📋 TEST SUMMARY REPORT")
        logger.info("="*60)
        
        total_urls = len(self.test_results)
        logger.info(f"\nTotal URLs Tested: {total_urls}")
        
        # Method-specific stats
        trafilatura_stats = {
            "successful": 0,
            "failed": 0,
            "errors": 0,
            "total_time": 0
        }
        beautifulsoup_stats = {
            "successful": 0,
            "failed": 0,
            "errors": 0,
            "total_time": 0
        }
        
        for result in self.test_results:
            traf = result["trafilatura"]
            bs = result["beautifulsoup"]
            
            if traf["status"] == "success":
                trafilatura_stats["successful"] += 1
            elif traf["status"] == "failed":
                trafilatura_stats["failed"] += 1
            else:
                trafilatura_stats["errors"] += 1
            trafilatura_stats["total_time"] += traf["extraction_time"]
            
            if bs["status"] == "success":
                beautifulsoup_stats["successful"] += 1
            elif bs["status"] == "failed":
                beautifulsoup_stats["failed"] += 1
            else:
                beautifulsoup_stats["errors"] += 1
            beautifulsoup_stats["total_time"] += bs["extraction_time"]
        
        logger.info("\n📊 TRAFILATURA STATS:")
        logger.info(f"  ✅ Successful: {trafilatura_stats['successful']}")
        logger.info(f"  ❌ Failed: {trafilatura_stats['failed']}")
        logger.info(f"  ⚠️  Errors: {trafilatura_stats['errors']}")
        logger.info(f"  ⏱️  Avg Time: {trafilatura_stats['total_time']/total_urls:.2f}s")
        
        logger.info("\n📊 BEAUTIFULSOUP STATS:")
        logger.info(f"  ✅ Successful: {beautifulsoup_stats['successful']}")
        logger.info(f"  ❌ Failed: {beautifulsoup_stats['failed']}")
        logger.info(f"  ⚠️  Errors: {beautifulsoup_stats['errors']}")
        logger.info(f"  ⏱️  Avg Time: {beautifulsoup_stats['total_time']/total_urls:.2f}s")
        
        # Method comparison
        logger.info("\n🔄 METHOD COMPARISON:")
        both_successful = sum(1 for r in self.test_results if r["comparison"]["both_successful"])
        logger.info(f"  Both methods successful: {both_successful}/{total_urls}")
        
        # Field agreement stats
        if both_successful > 0:
            field_agreement = {
                "title": 0,
                "meta_description": 0,
                "body_text": 0
            }
            
            for result in self.test_results:
                if result["comparison"]["both_successful"]:
                    for field in ["title", "meta_description", "body_text"]:
                        if result["comparison"]["field_differences"][field]["identical"]:
                            field_agreement[field] += 1
                            
            logger.info("\n  Field Agreement (when both successful):")
            for field, count in field_agreement.items():
                percentage = (count / both_successful) * 100
                logger.info(f"    {field}: {percentage:.1f}% identical ({count}/{both_successful})")
        
        # Common issues by method
        traf_warnings = []
        traf_errors = []
        bs_warnings = []
        bs_errors = []
        
        for result in self.test_results:
            traf_warnings.extend(result["trafilatura"]["warnings"])
            traf_errors.extend(result["trafilatura"]["errors"])
            bs_warnings.extend(result["beautifulsoup"]["warnings"])
            bs_errors.extend(result["beautifulsoup"]["errors"])
            
        if traf_warnings or bs_warnings:
            logger.info(f"\n⚠️  Common Warnings:")
            if traf_warnings:
                logger.info("  Trafilatura:")
                for warning in set(traf_warnings):
                    count = traf_warnings.count(warning)
                    logger.info(f"    - {warning} ({count} occurrences)")
            if bs_warnings:
                logger.info("  BeautifulSoup:")
                for warning in set(bs_warnings):
                    count = bs_warnings.count(warning)
                    logger.info(f"    - {warning} ({count} occurrences)")
                
        # Save detailed results
        self._save_test_results()
        
    def _save_test_results(self):
        """Save detailed test results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraper_test_results_{timestamp}.json"
        
        # Prepare results for JSON serialization
        json_results = []
        for result in self.test_results:
            json_result = {
                "url": result["url"],
                "trafilatura": self._prepare_method_result(result["trafilatura"]),
                "beautifulsoup": self._prepare_method_result(result["beautifulsoup"]),
                "comparison": result["comparison"]
            }
            json_results.append(json_result)
            
        with open(filename, 'w') as f:
            json.dump(json_results, f, indent=2, default=str)
            
        logger.info(f"\nDetailed results saved to: {filename}")
        
    def _prepare_method_result(self, method_result: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare method result for JSON serialization."""
        result = method_result.copy()
        if result["content"]:
            # Convert Pydantic model to dict
            result["content"] = result["content"].model_dump()
        return result


def main():
    """Run the scraper test suite."""
    # Define test URLs - you'll provide these
    test_urls = [
        # Add your test URLs here with expected values
        # Example format:
        # {
        #     "url": "https://example.com",
        #     "expected": {
        #         "title": "Expected Title",
        #         "meta_description": "Expected description"
        #     }
        # }
    ]
    
    # Example test URLs (you should replace with your actual test URLs)
    test_urls = [
        {
            "url": "https://hellowisp.com",
            "expected": {
                "title": "Wisp",  # Partial match expected
            }
        },
        # ADD YOUR 4 URLs HERE - Just replace these examples:
        {
            "url": "https://hellowisp.com/products/topical-spironolactone",
        },
        {
            "url": "https://hellowisp.com/at-home-testing-kits",
        },
        {
            "url": "https://hellowisp.com/products/plan-b",
        },
        {
            "url": "https://hellowisp.com/products/norethindrone",
        },
    ]
    
    # Run tests
    tester = ScraperTestSuite()
    
    print("\n🔧 SCRAPER TEST SUITE READY")
    print("Please provide test URLs to test, or press Enter to see example usage:")
    user_input = input().strip()
    
    if user_input:
        # Parse user input as URL
        test_urls = [{"url": user_input}]
        
    tester.run_test_suite(test_urls)


if __name__ == "__main__":
    main()