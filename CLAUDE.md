# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an SEM (Search Engine Marketing) Search Ad Copy Automation tool that generates Google Ads responsive search ad copy. The tool uses AI (Anthropic Claude) to create position-specific headlines and descriptions optimized for character limits and messaging strategies.

## Common Development Commands

```bash
# Set up virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the application
streamlit run app.py

# Test the scraper functionality
python test_scraper.py
```

## Architecture Overview

The application follows a layered architecture with clear separation of concerns:

### Core Components

1. **Models** (`src/models/`)
   - `ad_copy.py`: Pydantic models for request/response validation
   - `ad_schemas.py`: JSON schemas for structured AI output

2. **Services** (`src/services/`)
   - `ad_generator.py`: AI integration using Anthropic Claude API for generating ad copy
   - `scraper.py`: Web scraping to extract landing page content
   - `supabase_logger.py`: Optional database logging for tracking generations

3. **UI** (`app.py`)
   - Streamlit-based web interface
   - Handles user input, displays results, and manages CSV export

### Key Design Patterns

- **Structured Output**: Uses JSON schemas to ensure consistent AI responses
- **Position-Based Strategy**: Different copy types for positions 1-3 vs 4-15
- **Character Validation**: Strict limits of 30 chars (headlines) and 90 chars (descriptions)
- **Dynamic Insertions**: Supports {KeyWord:} and {LOCATION(City):} placeholders

## Important Implementation Details

### Ad Copy Generation Rules
- Generate 3 variants per request
- Each variant has 15 headlines and 4 descriptions
- Position 1-3: Branded messaging (must include brand name inferred from landing page)
- Position 4-15: Various copy types (benefits, social proof, CTAs, etc.)
- All copy must respect character limits

### API Configuration
The application uses Anthropic's Claude model (claude-sonnet-4-20250514). Ensure the following environment variables are set in `.env`:
- `ANTHROPIC_API_KEY`
- `SUPABASE_URL` (optional)
- `SUPABASE_KEY` (optional)

### Data Flow
1. User provides URL, keyword, and brand name
2. Scraper extracts landing page content
3. AI generates structured ad copy following position rules
4. Results displayed in UI with character validation
5. Export to CSV in agency-standard format

## Testing Approach

Currently, manual testing is done via:
- `test_scraper.py` for scraping functionality
- Streamlit UI for end-to-end testing
- Character limit validation is built into the models

## Key Files to Understand

- `ad-copy-generator-specs.md`: Detailed MVP specifications
- `src/models/ad_copy.py`: Core data structures
- `src/services/ad_generator.py`: AI integration logic
- `prompts/system_prompt.txt`: AI instructions for generating copy