"""Test the updated AdCopyGenerator with code execution for character limits."""
import sys
sys.path.append('.')

from src.services.ad_generator import AdCopyGenerator
from src.services.scraper import LandingPageScraper
from src.models.ad_copy import AdCopyRequest
import json
import logging

# Enable logging to see debug output
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')

def test_character_limits():
    """Test character limit adherence with the updated generator."""
    
    # Initialize services
    generator = AdCopyGenerator()
    scraper = LandingPageScraper()
    
    # Test URL
    test_url = "https://hellowisp.com/products/plan-b"
    
    print("Testing AdCopyGenerator with code execution beta...")
    print("="*60)
    
    # Scrape the landing page
    print(f"Scraping: {test_url}")
    landing_page = scraper.extract_content(test_url)
    
    if not landing_page:
        print("Failed to scrape landing page")
        return
    
    # Create a request for 10 headlines and 4 descriptions
    request = AdCopyRequest(
        brand="Wisp",
        target_keyword="plan b",
        headline_1_type="brand_mention",  # Use valid enum value
        landing_page_url=test_url,  # Correct field name
        headline_1_count=3,  # Branded headlines
        headline_2_count=3,  # Position 2
        headline_3_count=4,  # Position 3  
        description_1_count=2,
        description_2_count=2
    )
    
    print(f"\nGenerating ad copy for:")
    print(f"- Brand: {request.brand}")
    print(f"- Keyword: {request.target_keyword}")
    print(f"- Headlines: {request.headline_1_count + request.headline_2_count + request.headline_3_count} total")
    print(f"- Descriptions: {request.description_1_count + request.description_2_count} total")
    print("-"*60)
    
    # Generate ad copy
    result = generator.generate(request, landing_page)
    
    if not result:
        print("Failed to generate ad copy")
        return
    
    # Analyze results
    print("\nRESULTS:")
    print("="*60)
    
    # Group variants by position
    headlines_1 = [v for v in result.variants if v.asset_type == "Headline" and v.position_pinning == "1st"]
    headlines_2 = [v for v in result.variants if v.asset_type == "Headline" and v.position_pinning == "2nd"]
    headlines_3 = [v for v in result.variants if v.asset_type == "Headline" and v.position_pinning == "3rd"]
    descriptions_1 = [v for v in result.variants if v.asset_type == "Description" and v.position_pinning == "1st"]
    descriptions_2 = [v for v in result.variants if v.asset_type == "Description" and v.position_pinning == "2nd"]
    
    # Display results
    print("\nHeadlines Position 1:")
    for h in headlines_1:
        status = "✓" if h.character_count <= 30 else "✗"
        print(f"  {status} [{h.character_count:2d} chars] {h.asset}")
    
    print("\nHeadlines Position 2:")
    for h in headlines_2:
        status = "✓" if h.character_count <= 30 else "✗"
        print(f"  {status} [{h.character_count:2d} chars] {h.asset}")
        
    print("\nHeadlines Position 3:")
    for h in headlines_3:
        status = "✓" if h.character_count <= 30 else "✗"
        print(f"  {status} [{h.character_count:2d} chars] {h.asset}")
    
    # Descriptions
    print("\nDescriptions Position 1:")
    for d in descriptions_1:
        status = "✓" if d.character_count <= 90 else "✗"
        print(f"  {status} [{d.character_count:2d} chars] {d.asset}")
        
    print("\nDescriptions Position 2:")
    for d in descriptions_2:
        status = "✓" if d.character_count <= 90 else "✗"
        print(f"  {status} [{d.character_count:2d} chars] {d.asset}")
    
    # Summary statistics
    print("\n" + "="*60)
    print("SUMMARY:")
    print("="*60)
    
    # Calculate totals from all variants
    all_headlines = [v for v in result.variants if v.asset_type == "Headline"]
    all_descriptions = [v for v in result.variants if v.asset_type == "Description"]
    
    total_headlines = len(all_headlines)
    valid_headlines = sum(1 for h in all_headlines if h.character_count <= 30)
    
    total_descriptions = len(all_descriptions)
    valid_descriptions = sum(1 for d in all_descriptions if d.character_count <= 90)
    
    print(f"Headlines: {valid_headlines}/{total_headlines} within 30 char limit ({valid_headlines/total_headlines*100:.1f}%)")
    print(f"Descriptions: {valid_descriptions}/{total_descriptions} within 90 char limit ({valid_descriptions/total_descriptions*100:.1f}%)")
    print(f"\nGeneration ID: {result.generation_id}")
    
    # Save output with timestamp as text file
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_output_{timestamp}.txt"
    
    with open(filename, "w") as f:
        f.write(f"Test Output - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*60 + "\n\n")
        
        # Write all variants in a readable format
        f.write("HEADLINES:\n")
        f.write("-"*40 + "\n")
        for h in all_headlines:
            f.write(f"Position {h.position_pinning}: [{h.character_count:2d} chars] {h.asset}\n")
        
        f.write("\n\nDESCRIPTIONS:\n")
        f.write("-"*40 + "\n")
        for d in all_descriptions:
            f.write(f"Position {d.position_pinning}: [{d.character_count:2d} chars] {d.asset}\n")
        
        f.write(f"\n\nGeneration ID: {result.generation_id}\n")
    
    print(f"\nFull output saved to: {filename}")

if __name__ == "__main__":
    test_character_limits()