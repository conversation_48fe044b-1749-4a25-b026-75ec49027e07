"""Test script for the landing page scraper with Wisp URLs."""
from src.services.scraper import LandingPageScraper
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)

def test_wisp_urls():
    """Test the scraper with actual Wisp landing pages."""
    scraper = LandingPageScraper()
    
    # Wisp test URLs
    test_urls = [
        "https://hellowisp.com/shop/reproductive-health/emergency-contraception",
        "https://hellowisp.com/products/skip-your-period",
        "https://hellowisp.com/products/plan-b"
    ]
    
    for url in test_urls:
        print(f"\n{'='*80}")
        print(f"Testing: {url}")
        print(f"{'='*80}")
        
        # Validate URL
        is_valid = scraper.validate_url(url)
        print(f"URL Valid: {is_valid}")
        
        if not is_valid:
            continue
        
        # Extract content
        print("Extracting content...")
        content = scraper.extract_content(url)
        
        if content:
            print(f"✓ Successfully extracted content\n")
            
            # Display extracted data
            print(f"Title: {content.title}")
            print(f"Meta Description: {content.meta_description}")
            
            print(f"\nH1 Tags ({len(content.h1_tags)}):")
            for i, h1 in enumerate(content.h1_tags, 1):
                print(f"  {i}. {h1}")
            
            print(f"\nH2 Tags ({len(content.h2_tags)}):")
            for i, h2 in enumerate(content.h2_tags[:5], 1):  # Show first 5
                print(f"  {i}. {h2}")
            if len(content.h2_tags) > 5:
                print(f"  ... and {len(content.h2_tags) - 5} more")
            
            print(f"\nH3 Tags ({len(content.h3_tags)}):")
            for i, h3 in enumerate(content.h3_tags[:5], 1):  # Show first 5
                print(f"  {i}. {h3}")
            if len(content.h3_tags) > 5:
                print(f"  ... and {len(content.h3_tags) - 5} more")
            
            print(f"\nBody Text Statistics:")
            print(f"  - Total Length: {len(content.body_text)} characters")
            print(f"  - Word Count: {len(content.body_text.split())} words")
            
            # Show key content snippets
            print(f"\nBody Text Preview (first 1000 chars):")
            print("-" * 40)
            print(content.body_text[:1000])
            print("..." if len(content.body_text) > 1000 else "[END]")
            
            # Check for important keywords
            keywords = ["FDA", "prescription", "online", "delivery", "doctor", "consultation", "price", "$"]
            print(f"\nKey Terms Found:")
            for keyword in keywords:
                if keyword.lower() in content.body_text.lower():
                    print(f"  ✓ {keyword}")
            
        else:
            print("✗ Failed to extract content")

if __name__ == "__main__":
    test_wisp_urls()