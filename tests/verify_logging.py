"""Verification script to test the enhanced logging system."""
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
from supabase import create_client

load_dotenv()

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

if not supabase_url or not supabase_key:
    print("❌ SUPABASE_URL and SUPABASE_KEY must be set in .env file")
    exit(1)

try:
    supabase = create_client(supabase_url, supabase_key)
    print("✅ Connected to Supabase successfully")
except Exception as e:
    print(f"❌ Failed to connect to Supabase: {e}")
    exit(1)

# Get recent logs (last hour)
one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()

try:
    result = supabase.table('ad_generation_logs') \
        .select("*") \
        .gte('created_at', one_hour_ago) \
        .order('created_at', desc=True) \
        .execute()
    
    print(f"\n📊 Found {len(result.data)} logs in the last hour")
    
    if result.data:
        print("\nRecent generations:")
        for log in result.data[:5]:  # Show first 5
            print(f"  - {log['created_at']}: {log['brand_name']} - {log['variant_angle']} ({log['status']})")
            if log.get('error_message'):
                print(f"    Error: {log['error_message']}")
        
        # Show token usage stats
        total_tokens = sum(log.get('total_tokens', 0) for log in result.data if log.get('total_tokens'))
        avg_latency = sum(log.get('latency_ms', 0) for log in result.data if log.get('latency_ms')) / len(result.data)
        
        print(f"\n📈 Stats:")
        print(f"  - Total tokens used: {total_tokens:,}")
        print(f"  - Average latency: {avg_latency:.0f}ms")
    else:
        print("\nNo logs found. Try generating some ad copy first!")
        
except Exception as e:
    print(f"\n❌ Error fetching logs: {e}")
    print("Make sure the 'ad_generation_logs' table exists in your Supabase project")

# Test generation retrieval
print("\n🔍 Testing generation retrieval...")
try:
    # Get the most recent generation
    result = supabase.table('ad_generation_logs') \
        .select("generation_id, brand, target_keyword, headline_1_type") \
        .order('created_at', desc=True) \
        .limit(1) \
        .execute()
    
    if result.data:
        gen = result.data[0]
        print(f"  Found recent generation: {gen['generation_id'][:8]}...")
        print(f"    Brand: {gen['brand']}")
        print(f"    Keyword: {gen['target_keyword']}")
        print(f"    Headline Type: {gen['headline_1_type']}")
        
        # Get full details
        detail_result = supabase.table('ad_generation_logs') \
            .select("generated_headlines_p1, generated_descriptions_p1") \
            .eq('generation_id', gen['generation_id']) \
            .execute()
        
        if detail_result.data:
            data = detail_result.data[0]
            if data.get('generated_headlines_p1'):
                print(f"    Headlines P1: {len(data['generated_headlines_p1'])} generated")
            if data.get('generated_descriptions_p1'):
                print(f"    Descriptions P1: {len(data['generated_descriptions_p1'])} generated")
    else:
        print("  No generations found yet")
        
except Exception as e:
    print(f"  Error testing generation retrieval: {e}")